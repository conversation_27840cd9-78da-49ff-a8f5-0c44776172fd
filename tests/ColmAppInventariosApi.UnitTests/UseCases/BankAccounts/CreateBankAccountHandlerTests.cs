﻿using Ardalis.SharedKernel;
using AutoMapper;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate.Enums;
using ColmAppInventariosApi.Core.Interfaces;
using ColmAppInventariosApi.UseCases.BankAccounts.Commands.Create;
using ColmAppInventariosApi.UseCases.BankAccounts.Dtos;
using FluentAssertions;
using Moq;

namespace ColmAppInventariosApi.UnitTests.UseCases.BankAccounts;
public class CreateBankAccountHandlerTests
{
  private readonly Mock<IRepository<BankAccount>> _mockRepository;
  private readonly Mock<IMapper> _mockMapper;
  private readonly Mock<ILogService> _mockLogService;
  private readonly CreateBankAccountHandler _handler;

  public CreateBankAccountHandlerTests()
  {
    _mockRepository = new Mock<IRepository<BankAccount>>();
    _mockMapper = new Mock<IMapper>();
    _mockLogService = new Mock<ILogService>();
    _handler = new CreateBankAccountHandler(
        _mockRepository.Object,
        _mockMapper.Object,
        _mockLogService.Object
    );
  }

  [Fact]
  public async Task Handle_ConDatosValidos_DeberiaCrearCuentaBancaria()
  {
    // Arrange
    var requestDto = new BankAccountRequestDto
    {
      AccountNumber = "*********",
      AccountHolderName = "John Doe",
      Balance = 1000,
      AccountType = AccountType.Savings
    };

    var command = new CreateBankAccountCommand(requestDto);

    var bankAccount = new BankAccount
    {
      Id = Guid.NewGuid(),
      AccountNumber = "*********",
      AccountHolderName = "John Doe",
      Balance = 1000,
      AccountType = AccountType.Savings
    };

    var responseDto = new BankAccounResponseDto
    {
      Id = bankAccount.Id,
      AccountNumber = "*********",
      AccountHolderName = "John Doe",
      Balance = 1000,
      AccountType = AccountType.Savings
    };

    _mockMapper.Setup(m => m.Map<BankAccount>(requestDto))
        .Returns(bankAccount);
    _mockMapper.Setup(m => m.Map<BankAccounResponseDto>(bankAccount))
        .Returns(responseDto);
    _mockRepository.Setup(r => r.AddAsync(bankAccount, It.IsAny<CancellationToken>()))
        .Returns(Task.FromResult(bankAccount));

    // Act
    var result = await _handler.Handle(command, CancellationToken.None);

    // Assert
    result.Should().NotBeNull();
    result.IsSuccess.Should().BeTrue();
    result.Value.Should().Be(responseDto);

    _mockLogService.Verify(l => l.LogInfoAsync(It.Is<string>(s =>
        s.Contains("Creando nueva cuenta bancaria") && s.Contains(requestDto.AccountHolderName))),
        Times.Once);
    _mockLogService.Verify(l => l.LogSuccessAsync(It.Is<string>(s =>
        s.Contains("Cuenta bancaria creada exitosamente") &&
        s.Contains(bankAccount.Id.ToString()) &&
        s.Contains(bankAccount.AccountHolderName))),
        Times.Once);
    _mockRepository.Verify(r => r.AddAsync(bankAccount, It.IsAny<CancellationToken>()), Times.Once);
  }

  [Fact]
  public async Task Handle_CuandoOcurreError_DeberiaRetornarResultError()
  {
    // Arrange
    var requestDto = new BankAccountRequestDto
    {
      AccountNumber = "*********",
      AccountHolderName = "John Doe",
      Balance = 1000,
      AccountType = AccountType.Savings
    };

    var command = new CreateBankAccountCommand(requestDto);

    var bankAccount = new BankAccount
    {
      AccountNumber = "*********",
      AccountHolderName = "John Doe",
      Balance = 1000,
      AccountType = AccountType.Savings
    };

    var expectedError = new Exception("Error al crear cuenta bancaria");

    _mockMapper.Setup(m => m.Map<BankAccount>(requestDto))
        .Returns(bankAccount);
    _mockRepository.Setup(r => r.AddAsync(bankAccount, It.IsAny<CancellationToken>()))
        .ThrowsAsync(expectedError);

    // Act
    var result = await _handler.Handle(command, CancellationToken.None);

    // Assert
    result.Should().NotBeNull();
    result.IsSuccess.Should().BeFalse();
    result.Errors.Should().Contain(expectedError.Message);

    _mockLogService.Verify(l => l.LogErrorAsync(
        It.Is<string>(s => s.Contains("Error al crear cuenta bancaria") && s.Contains(requestDto.AccountHolderName)),
        It.Is<Exception>(e => e == expectedError)),
        Times.Once);
  }

  [Fact]
  public async Task Handle_ConMapperError_DeberiaRegistrarErrorYRetornarResultError()
  {
    // Arrange
    var requestDto = new BankAccountRequestDto
    {
      AccountNumber = "*********",
      AccountHolderName = "John Doe",
      Balance = 1000,
      AccountType = AccountType.Savings
    };

    var command = new CreateBankAccountCommand(requestDto);

    var expectedError = new AutoMapperMappingException("Error de mapeo");

    _mockMapper.Setup(m => m.Map<BankAccount>(requestDto))
        .Throws(expectedError);

    // Act
    var result = await _handler.Handle(command, CancellationToken.None);

    // Assert
    result.Should().NotBeNull();
    result.IsSuccess.Should().BeFalse();
    result.Errors.Should().Contain(expectedError.Message);

    _mockLogService.Verify(l => l.LogErrorAsync(
        It.Is<string>(s => s.Contains("Error al crear cuenta bancaria") && s.Contains(requestDto.AccountHolderName)),
        It.Is<Exception>(e => e == expectedError)),
        Times.Once);

    _mockRepository.Verify(r => r.AddAsync(It.IsAny<BankAccount>(), It.IsAny<CancellationToken>()), Times.Never);
  }

  [Fact]
  public async Task Handle_VerificarInformacionCorrectaEnRespuesta()
  {
    // Arrange
    var requestDto = new BankAccountRequestDto
    {
      AccountNumber = "*********",
      AccountHolderName = "John Doe",
      Balance = 1000,
      AccountType = AccountType.Savings
    };

    var command = new CreateBankAccountCommand(requestDto);

    var bankAccount = new BankAccount
    {
      Id = Guid.NewGuid(),
      AccountNumber = "*********",
      AccountHolderName = "John Doe",
      Balance = 1000,
      AccountType = AccountType.Credit
    };

    var responseDto = new BankAccounResponseDto
    {
      Id = bankAccount.Id,
      AccountNumber = "*********",
      AccountHolderName = "John Doe",
      Balance = 1000,
      AccountType = AccountType.Credit
    };

    _mockMapper.Setup(m => m.Map<BankAccount>(requestDto))
        .Returns(bankAccount);
    _mockMapper.Setup(m => m.Map<BankAccounResponseDto>(bankAccount))
        .Returns(responseDto);
    _mockRepository.Setup(r => r.AddAsync(bankAccount, It.IsAny<CancellationToken>()))
        .Returns(Task.FromResult(bankAccount));

    // Act
    var result = await _handler.Handle(command, CancellationToken.None);

    // Assert
    result.Should().NotBeNull();
    result.IsSuccess.Should().BeTrue();
    result.Value.Id.Should().Be(bankAccount.Id);
    result.Value.AccountNumber.Should().Be("*********");
    result.Value.AccountHolderName.Should().Be("John Doe");
    result.Value.Balance.Should().Be(1000);
    result.Value.AccountType.Should().Be(AccountType.Credit);
  }

  [Fact]
  public async Task Handle_CuandoSeFallaEnRepositorio_DeberiaRegistrarErrorYRetornarResultError()
  {
    // Arrange
    var requestDto = new BankAccountRequestDto
    {
      AccountNumber = "*********",
      AccountHolderName = "John Doe",
      Balance = 1000,
      AccountType = AccountType.Savings
    };

    var command = new CreateBankAccountCommand(requestDto);

    var bankAccount = new BankAccount
    {
      Id = Guid.NewGuid(),
      AccountNumber = "*********",
      AccountHolderName = "John Doe",
      Balance = 1000,
      AccountType = AccountType.Savings
    };

    var dbException = new Exception("Error al acceder a la base de datos");

    _mockMapper.Setup(m => m.Map<BankAccount>(requestDto))
        .Returns(bankAccount);
    _mockRepository.Setup(r => r.AddAsync(bankAccount, It.IsAny<CancellationToken>()))
        .ThrowsAsync(dbException);

    // Act
    var result = await _handler.Handle(command, CancellationToken.None);

    // Assert
    result.Should().NotBeNull();
    result.IsSuccess.Should().BeFalse();
    result.Errors.Should().Contain(dbException.Message);

    _mockLogService.Verify(l => l.LogErrorAsync(
        It.Is<string>(s => s.Contains("Error al crear cuenta bancaria")),
        It.Is<Exception>(e => e == dbException)),
        Times.Once);
  }
}
