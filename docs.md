# ColmAppInventariosApi - Project Documentation

## Overview

**ColmAppInventariosApi** is a .NET 8 Web API project built using **Clean Architecture** principles. It serves as a production-ready template for building robust and maintainable APIs with a focus on separation of concerns, dependency inversion, and domain-driven design (DDD).

The project is based on the popular Clean Architecture template by <PERSON> (@ardalis) but has been customized with specific configurations and includes a sample **Bank Account** domain to demonstrate the architectural concepts.

## Architecture

### Clean Architecture Layers

The solution follows the standard Clean Architecture pattern with four distinct layers:

#### 1. **Core Layer** (`ColmAppInventariosApi.Core`)
- **Purpose**: Contains the domain model and business rules
- **Dependencies**: None (innermost layer)
- **Contents**:
  - Domain entities and aggregates
  - Value objects and enumerations
  - Domain events and handlers
  - Business specifications
  - Core interfaces and contracts

#### 2. **Infrastructure Layer** (`ColmAppInventariosApi.Infrastructure`)
- **Purpose**: Implements external concerns and infrastructure services
- **Dependencies**: Core layer only
- **Contents**:
  - Entity Framework Core data access
  - External service implementations
  - Database migrations
  - AutoMapper configurations
  - Logging services

#### 3. **Use Cases Layer** (`ColmAppInventariosApi.UseCases`)
- **Purpose**: Contains application logic and orchestrates business operations
- **Dependencies**: Core layer only
- **Contents**:
  - CQRS Commands and Queries
  - MediatR handlers
  - Data Transfer Objects (DTOs)
  - FluentValidation validators
  - Application-specific interfaces

#### 4. **Web Layer** (`ColmAppInventariosApi.Web`)
- **Purpose**: Handles HTTP requests and responses (presentation layer)
- **Dependencies**: Infrastructure and UseCases layers
- **Contents**:
  - FastEndpoints API endpoints
  - Global exception handlers
  - Configuration classes
  - Swagger/OpenAPI setup

## Technology Stack

### Core Technologies
- **.NET 8**: Latest LTS version of .NET
- **ASP.NET Core 8**: Web framework
- **Entity Framework Core 8**: Object-relational mapping
- **SQL Server**: Primary database (configurable)

### Architectural Patterns
- **Clean Architecture**: Separation of concerns with dependency inversion
- **Domain-Driven Design (DDD)**: Focus on domain modeling
- **CQRS**: Command Query Responsibility Segregation using MediatR
- **Repository Pattern**: Data access abstraction
- **Specification Pattern**: Encapsulating query logic

### Key Libraries and Frameworks
- **FastEndpoints**: Modern alternative to controllers for API endpoints
- **MediatR**: In-process messaging for CQRS implementation
- **AutoMapper**: Object-to-object mapping
- **FluentValidation**: Robust input validation
- **Autofac**: Dependency injection container
- **Serilog**: Structured logging
- **Swagger/OpenAPI**: API documentation
- **Ardalis.Specification**: Specification pattern implementation
- **xUnit**: Unit testing framework

## Domain Model

### Sample Domain: Banking System

The project includes a sample banking domain to demonstrate the architecture:

#### Entities
- **BankAccount**: Aggregate root representing a bank account
  - Properties: AccountNumber, AccountHolderName, Balance, AccountType
  - Navigation: Collection of BankTransactions

- **BankTransaction**: Entity representing financial transactions
  - Properties: Amount, TransactionType, Description, TransactionDate
  - Relationship: Belongs to a BankAccount

#### Enumerations
- **AccountType**: Checking, Savings, etc.
- **TransactionType**: Deposit, Withdrawal, Transfer

#### Domain Events
- **BankTransactionCreatedEvent**: Raised when a new transaction is created

## API Endpoints

### Bank Accounts
- `GET /api/BankAccounts` - List all bank accounts
- `GET /api/BankAccounts/{id}` - Get bank account by ID
- `POST /api/BankAccounts` - Create new bank account
- `PUT /api/BankAccounts/{id}` - Update existing bank account
- `DELETE /api/BankAccounts/{id}` - Delete bank account

### Bank Transactions
- `GET /api/BankTransactions` - List transactions (paginated)
- `POST /api/BankTransactions` - Create new transaction

### Health Checks
- Health check endpoints for monitoring application status

## Configuration

### Database Configuration
- Connection strings configured in `appsettings.json`
- Entity Framework migrations in `Infrastructure/Migrations`
- Database context: `AppDbContext`

### Dependency Injection
- **Autofac modules** for organizing registrations:
  - `DefaultCoreModule`: Core layer dependencies
  - `AutofacInfrastructureModule`: Infrastructure dependencies
- Built-in .NET DI for basic services

### Logging
- **Serilog** configured for structured logging
- Request logging middleware
- Custom log service implementation

## Development Workflow

### Project Structure
- **Solution file**: `ColmAppInventariosApi.sln`
- **Central package management**: `Directory.Packages.props`
- **Global build properties**: `Directory.Build.props`
- **.NET SDK version**: Specified in `global.json`

### Running the Application
1. Configure database connection in `appsettings.json`
2. Apply EF migrations: `dotnet ef database update`
3. Run the API: `dotnet run --project src/ColmAppInventariosApi.Web`
4. Access Swagger UI at `/swagger`

### Testing
- Unit tests in `ColmAppInventariosApi.UnitTests`
- Test framework: xUnit with FluentAssertions
- Mocking: NSubstitute and Moq

## Key Features

### CQRS Implementation
- Commands for write operations (Create, Update, Delete)
- Queries for read operations (Get, List)
- MediatR pipeline with validation behaviors

### Validation
- FluentValidation for input validation
- Validation pipeline behavior in MediatR
- Automatic validation error responses

### Error Handling
- Global exception handler
- Structured error responses
- Proper HTTP status codes

### API Documentation
- Swagger/OpenAPI integration
- Custom Swagger UI styling
- Comprehensive endpoint documentation

### Health Monitoring
- Built-in health checks
- Custom health check endpoints
- Application status monitoring

## Best Practices Implemented

1. **Separation of Concerns**: Clear layer boundaries
2. **Dependency Inversion**: Dependencies point inward
3. **Single Responsibility**: Each class has one reason to change
4. **Domain-Driven Design**: Rich domain model
5. **SOLID Principles**: Applied throughout the codebase
6. **Clean Code**: Readable and maintainable code structure
7. **Testing**: Comprehensive unit test coverage
8. **Configuration**: Environment-specific settings
9. **Logging**: Structured and contextual logging
10. **Documentation**: Self-documenting API with Swagger

## Detailed Layer Analysis

### Core Layer Deep Dive

The Core layer is the heart of the application and contains:

#### Abstract Base Classes
- **BaseEntity**: Provides common entity properties (Id, CreatedAt, UpdatedAt)
- **BaseResponseDto**: Standard response structure for API responses
- **PaginationFilter**: Base class for pagination parameters
- **PaginationResult**: Wrapper for paginated results

#### Domain Aggregates
Following DDD principles, the domain is organized into aggregates:

**BankAccountAggregate**:
- **Aggregate Root**: BankAccount entity
- **Child Entities**: BankTransaction
- **Value Objects**: Account types, transaction types
- **Domain Events**: Transaction creation events
- **Specifications**: Query specifications for complex filtering

#### Interfaces and Contracts
- Service interfaces (e.g., ILogService)
- Repository contracts
- Domain service interfaces

### Infrastructure Layer Deep Dive

#### Data Access
- **AppDbContext**: EF Core context with entity configurations
- **EfRepository**: Generic repository implementation using Ardalis.Specification
- **Migrations**: Database schema versioning

#### External Services
- **LogService**: Concrete implementation of logging interface
- **AutoMapper Profiles**: Object mapping configurations

#### Dependency Registration
- **AutofacInfrastructureModule**: Registers infrastructure dependencies
- Environment-specific configurations

### Use Cases Layer Deep Dive

#### CQRS Implementation
**Commands** (Write Operations):
- Create, Update, Delete operations
- Each command has its own handler
- Validation through FluentValidation
- Business rule enforcement

**Queries** (Read Operations):
- Data retrieval operations
- Optimized for reading
- Support for pagination and filtering
- DTO projections

#### Common Components
- **Behaviors**: Cross-cutting concerns (validation, logging)
- **Pagination**: Reusable pagination utilities
- **DTOs**: Data transfer objects for API contracts

### Web Layer Deep Dive

#### FastEndpoints Architecture
- **Endpoint Classes**: One class per endpoint
- **Request/Response Models**: Strongly typed
- **Validation Integration**: Automatic validation
- **Swagger Integration**: Auto-generated documentation

#### Configuration Classes
- **SqlConfig**: Database connection setup
- **SwaggerConfig**: API documentation configuration
- **UIHealthChecksConfig**: Health monitoring setup

#### Global Handlers
- **GlobalExceptionHandler**: Centralized error handling
- **Validation Middleware**: Input validation
- **Logging Middleware**: Request/response logging

## Development Guidelines

### Adding New Features

1. **Start with Domain**: Define entities, value objects, and business rules in Core
2. **Define Use Cases**: Create commands/queries in UseCases layer
3. **Implement Infrastructure**: Add repositories, services in Infrastructure
4. **Create Endpoints**: Add FastEndpoints in Web layer
5. **Write Tests**: Add unit tests for business logic

### Code Organization Patterns

#### Folder Structure Convention
```
Feature/
├── Commands/
│   ├── Create/
│   ├── Update/
│   └── Delete/
├── Queries/
│   ├── GetById/
│   ├── GetList/
│   └── GetPaginated/
├── Dtos/
├── Validators/
└── Specifications/
```

#### Naming Conventions
- **Commands**: `{Action}{Entity}Command` (e.g., CreateBankAccountCommand)
- **Handlers**: `{Action}{Entity}Handler` (e.g., CreateBankAccountHandler)
- **DTOs**: `{Entity}{Purpose}Dto` (e.g., BankAccountResponseDto)
- **Validators**: `{Dto}Validator` (e.g., BankAccountRequestDtoValidator)

### Testing Strategy

#### Unit Tests
- Test business logic in isolation
- Mock external dependencies
- Focus on Core and UseCases layers
- Use AAA pattern (Arrange, Act, Assert)

#### Integration Tests
- Test layer interactions
- Use in-memory database for data tests
- Test complete request/response cycles

#### Test Organization
```
Tests/
├── Core/
│   ├── Entities/
│   ├── Specifications/
│   └── Events/
├── UseCases/
│   ├── Commands/
│   └── Queries/
└── Integration/
    ├── Endpoints/
    └── Database/
```

## Deployment and Operations

### Environment Configuration
- **Development**: Local development settings
- **Staging**: Pre-production environment
- **Production**: Live environment settings

### Database Management
- **Migrations**: Version-controlled schema changes
- **Seeding**: Initial data setup
- **Backup Strategy**: Regular database backups

### Monitoring and Logging
- **Serilog**: Structured logging with multiple sinks
- **Health Checks**: Application health monitoring
- **Performance Metrics**: Request timing and throughput

### Security Considerations
- **Authentication**: Negotiate authentication (configurable)
- **Authorization**: Role-based access control
- **CORS**: Cross-origin resource sharing configuration
- **HTTPS**: Enforced in production
- **Input Validation**: Comprehensive validation at API boundary

## Extensibility Points

### Adding New Domains
1. Create new aggregate in Core layer
2. Add corresponding use cases
3. Implement infrastructure services
4. Create API endpoints
5. Add tests

### Integrating External Services
1. Define interface in Core
2. Implement in Infrastructure
3. Register in Autofac module
4. Use in use case handlers

### Custom Behaviors
1. Implement IPipelineBehavior<TRequest, TResponse>
2. Register in MediatR pipeline
3. Apply cross-cutting concerns

This architecture provides a solid foundation for building scalable, maintainable, and testable web APIs while following industry best practices and clean architecture principles.
