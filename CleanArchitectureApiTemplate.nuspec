<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2010/07/nuspec.xsd">
  <metadata>
    <id>ColmAppInventariosApi</id>
    <title>Custom Clean Architecture API Template .NET 8</title>
    <version>1.0.0</version> <!-- Reset version for custom template -->
    <authors><PERSON></authors>
    <description>
      A custom .NET 8 Clean Architecture API template based on <PERSON><PERSON><PERSON>'s template, featuring a Bank Account example domain. Provides a starting point for building robust and maintainable APIs.
    </description>
    <language>en-US</language>
    <license type="expression">MIT</license> <!-- Assuming MIT license from original -->
    <projectUrl>https://github.com/Gabriel7729/cleanarch.backend.csharp</projectUrl>
    <releaseNotes>
      Initial release of the custom template.
    </releaseNotes>
    <!-- packageType removed as template.json handles template definition -->
    <tags>Web API ASP.NET Clean Architecture .NET 8 C# Bank custom-template</tags>
    <!-- <icon>content/.template.config/icon.png</icon> Icon removed as file doesn't exist -->
    <readme>README.md</readme>
  </metadata>
  <files>
    <!-- Include all files except excluded ones, placing them in 'content' -->
    <file src=".\**" target="content" exclude="**\bin\**;**\obj\**;**\.git\**;**\.github\**;**\*.user;**\.vs\**;**\.vscode\**;**\.gitignore;**\sample\**;*.nupkg" />
    <!-- Explicitly include the template config -->
    <file src=".template.config\**" target="content\.template.config" />
    <!-- Include README at root -->
    <file src="README.md" target="" />
    <!-- Icon file reference removed -->
    <!-- <file src=".template.config\icon.png" target="" /> -->
  </files>
</package>
