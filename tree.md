# Project Structure Tree

```
ColmAppInventariosApi/
├── 📁 src/                                    # Source code directory
│   ├── 📁 ColmAppInventariosApi.Core/         # Domain layer (Clean Architecture Core)
│   │   ├── 📁 Abstract/                       # Base classes and common abstractions
│   │   │   ├── BaseEntity.cs                  # Base entity with common properties
│   │   │   ├── BaseResponseDto.cs             # Base response DTO structure
│   │   │   ├── PaginationFilter.cs            # Pagination filter parameters
│   │   │   └── PaginationResult.cs            # Pagination result wrapper
│   │   ├── 📁 Aggregates/                     # Domain aggregates (DDD)
│   │   │   ├── 📁 AbstractAggregate/          # Abstract aggregate base
│   │   │   └── 📁 BankAccountAggregate/       # Bank account domain aggregate
│   │   │       ├── BankAccount.cs             # Bank account entity (aggregate root)
│   │   │       ├── BankTransaction.cs         # Bank transaction entity
│   │   │       ├── 📁 Enums/                  # Domain enumerations
│   │   │       │   ├── AccountType.cs         # Account type enumeration
│   │   │       │   └── TransactionType.cs     # Transaction type enumeration
│   │   │       ├── 📁 Events/                 # Domain events
│   │   │       │   └── BankTransactionCreatedEvent.cs
│   │   │       ├── 📁 Handlers/               # Domain event handlers
│   │   │       │   └── BankTransactionCreatedHandler.cs
│   │   │       ├── 📁 Models/                 # Domain models and value objects
│   │   │       │   └── TransactionFilterParameters.cs
│   │   │       └── 📁 Specs/                  # Specification pattern implementations
│   │   │           ├── GetBankAccountByNumberSpec.cs
│   │   │           └── GetTransactionPaginateSpec.cs
│   │   ├── 📁 Interfaces/                     # Core interfaces and contracts
│   │   │   └── ILogService.cs                 # Logging service interface
│   │   ├── DefaultCoreModule.cs               # Autofac module for core dependencies
│   │   ├── ColmAppInventariosApi.Core.csproj  # Core project file
│   │   └── README.md                          # Core layer documentation
│   │
│   ├── 📁 ColmAppInventariosApi.Infrastructure/ # Infrastructure layer
│   │   ├── 📁 Config/                         # Configuration classes
│   │   │   └── MainMapperProfile.cs           # AutoMapper profile configuration
│   │   ├── 📁 Data/                           # Data access layer
│   │   │   ├── AppDbContext.cs                # Entity Framework DbContext
│   │   │   ├── EfRepository.cs                # Generic EF repository implementation
│   │   │   └── 📁 Extensions/                 # Data extensions
│   │   ├── 📁 Migrations/                     # Entity Framework migrations
│   │   │   ├── 20250408005758_Initial Migration.cs
│   │   │   ├── 20250408005758_Initial Migration.Designer.cs
│   │   │   └── AppDbContextModelSnapshot.cs
│   │   ├── 📁 Services/                       # Infrastructure services
│   │   │   └── LogService.cs                  # Logging service implementation
│   │   ├── AutofacInfrastructureModule.cs     # Autofac module for infrastructure
│   │   ├── ColmAppInventariosApi.Infrastructure.csproj
│   │   └── README.md                          # Infrastructure layer documentation
│   │
│   ├── 📁 ColmAppInventariosApi.UseCases/     # Application layer (Use Cases/CQRS)
│   │   ├── 📁 BankAccounts/                   # Bank account use cases
│   │   │   ├── 📁 Commands/                   # Command operations (Write)
│   │   │   │   ├── 📁 Create/                 # Create bank account command
│   │   │   │   │   ├── CreateBankAccountCommand.cs
│   │   │   │   │   └── CreateBankAccountHandler.cs
│   │   │   │   ├── 📁 Delete/                 # Delete bank account command
│   │   │   │   │   ├── DeleteBankAccountCommand.cs
│   │   │   │   │   └── DeleteBankAccountHandler.cs
│   │   │   │   └── 📁 Update/                 # Update bank account command
│   │   │   │       ├── UpdateBankAccountCommand.cs
│   │   │   │       └── UpdateBankAccountHandler.cs
│   │   │   ├── 📁 Dtos/                       # Data Transfer Objects
│   │   │   ├── 📁 Queries/                    # Query operations (Read)
│   │   │   └── 📁 Validators/                 # FluentValidation validators
│   │   ├── 📁 BankTransactions/               # Bank transaction use cases
│   │   │   ├── 📁 Commands/                   # Transaction command operations
│   │   │   ├── 📁 Dtos/                       # Transaction DTOs
│   │   │   ├── 📁 Queries/                    # Transaction query operations
│   │   │   └── 📁 Validators/                 # Transaction validators
│   │   ├── 📁 Commons/                        # Common use case components
│   │   │   ├── 📁 Behaviors/                  # MediatR pipeline behaviors
│   │   │   └── 📁 Paginations/                # Pagination utilities
│   │   ├── ColmAppInventariosApi.UseCases.csproj
│   │   └── README.md                          # Use cases layer documentation
│   │
│   └── 📁 ColmAppInventariosApi.Web/          # Presentation layer (Web API)
│       ├── 📁 Config/                         # Web configuration classes
│       │   ├── SqlConfig.cs                   # SQL Server configuration
│       │   ├── SwaggerConfig.cs               # Swagger/OpenAPI configuration
│       │   └── UIHealthChecksConfig.cs        # Health checks UI configuration
│       ├── 📁 Endpoints/                      # FastEndpoints API endpoints
│       │   ├── 📁 BankAccounts/               # Bank account endpoints
│       │   │   ├── Create.cs                  # POST /api/BankAccounts
│       │   │   ├── Delete.cs                  # DELETE /api/BankAccounts/{id}
│       │   │   ├── GetById.cs                 # GET /api/BankAccounts/{id}
│       │   │   ├── List.cs                    # GET /api/BankAccounts
│       │   │   ├── ListPaginated.cs           # GET /api/BankAccounts (paginated)
│       │   │   ├── ListPaginated.ListPaginatedBankAccountRequest.cs
│       │   │   └── Update.cs                  # PUT /api/BankAccounts/{id}
│       │   ├── 📁 BankTransactions/           # Bank transaction endpoints
│       │   ├── 📁 HealthChecks/               # Health check endpoints
│       │   └── BaseEndpoint.cs                # Base endpoint class
│       ├── 📁 Handlers/                       # Global handlers
│       │   └── GlobalExceptionHandler.cs      # Global exception handling
│       ├── 📁 Properties/                     # Project properties
│       │   └── launchSettings.json            # Launch configuration
│       ├── 📁 wwwroot/                        # Static web assets
│       │   └── 📁 swagger-ui/                 # Custom Swagger UI assets
│       ├── Program.cs                         # Application entry point
│       ├── appsettings.json                   # Application configuration
│       └── ColmAppInventariosApi.Web.csproj   # Web project file
│
├── 📁 tests/                                  # Test projects directory
│   └── 📁 ColmAppInventariosApi.UnitTests/    # Unit tests project
│       ├── 📁 UseCases/                       # Use case tests
│       │   └── 📁 BankAccounts/               # Bank account use case tests
│       └── ColmAppInventariosApi.UnitTests.csproj
│
├── 📄 ColmAppInventariosApi.sln               # Visual Studio solution file
├── 📄 Directory.Build.props                   # MSBuild properties for all projects
├── 📄 Directory.Packages.props                # Central package management
├── 📄 global.json                             # .NET SDK version configuration
├── 📄 nuget.config                            # NuGet package sources configuration
├── 📄 CleanArchitectureApiTemplate.nuspec     # NuGet template package specification
├── 📄 LICENSE                                 # Project license
└── 📄 README.md                               # Project documentation
```

## Key Directories Explanation

- **📁 src/**: Contains all source code organized by architectural layers
- **📁 Core/**: Domain layer with entities, aggregates, and business rules
- **📁 Infrastructure/**: Data access, external services, and infrastructure concerns
- **📁 UseCases/**: Application logic using CQRS pattern with MediatR
- **📁 Web/**: API presentation layer using FastEndpoints
- **📁 tests/**: All test projects for the solution
