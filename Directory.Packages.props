<Project>
  <ItemGroup>
    <PackageVersion Include="Ardalis.GuardClauses" Version="4.4.0" />
    <PackageVersion Include="Ardalis.HttpClientTestExtensions" Version="4.2.0" />
    <PackageVersion Include="Ardalis.ListStartupServices" Version="1.1.4" />
    <PackageVersion Include="Ardalis.Result" Version="7.2.0" />
    <PackageVersion Include="Ardalis.Result.AspNetCore" Version="7.2.0" />
    <PackageVersion Include="Ardalis.SharedKernel" Version="1.4.0" />
    <PackageVersion Include="Ardalis.SmartEnum" Version="7.0.0" />
    <PackageVersion Include="Ardalis.Specification" Version="8.0.0" />
    <PackageVersion Include="Ardalis.Specification.EntityFrameworkCore" Version="8.0.0" />
    <PackageVersion Include="Autofac" Version="7.1.0" />
    <PackageVersion Include="Autofac.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageVersion Include="AutoMapper" Version="13.0.1" />
    <PackageVersion Include="FastEndpoints" Version="5.20.1" />
    <PackageVersion Include="FastEndpoints.ApiExplorer" Version="2.2.0" />
    <PackageVersion Include="FastEndpoints.Swagger" Version="5.20.1" />
    <PackageVersion Include="FastEndpoints.Swagger.Swashbuckle" Version="2.2.0" />
    <PackageVersion Include="FluentValidation" Version="11.11.0" />
    <PackageVersion Include="FluentValidation.AspNetCore" Version="11.3.0" />
    <PackageVersion Include="FluentValidation.DependencyInjectionExtensions" Version="11.11.0" />
    <PackageVersion Include="MailKit" Version="4.3.0" />
    <PackageVersion Include="MediatR" Version="12.2.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.0" />
    <PackageVersion Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
    <PackageVersion Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.Negotiate" Version="8.0.2" />
    <PackageVersion Include="NSubstitute" Version="5.1.0" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="ReportGenerator" Version="5.2.0" />
    <PackageVersion Include="Serilog.AspNetCore" Version="8.0.0" />
    <PackageVersion Include="Serilog.Enrichers.Environment" Version="3.0.1" />
    <PackageVersion Include="Serilog.Enrichers.Thread" Version="4.0.0" />
    <PackageVersion Include="SQLite" Version="3.13.0" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageVersion Include="Swashbuckle.AspNetCore.Annotations" Version="6.5.0" />
    <PackageVersion Include="System.ServiceModel.Duplex" Version="4.10.0" />
    <PackageVersion Include="System.ServiceModel.Federation" Version="4.10.0" />
    <PackageVersion Include="System.ServiceModel.Http" Version="4.10.0" />
    <PackageVersion Include="System.ServiceModel.NetTcp" Version="4.10.0" />
    <PackageVersion Include="System.ServiceModel.Security" Version="4.10.0" />
    <!--Tests Packages -->
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageVersion Include="xunit" Version="2.5.3" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.5.3" />
    <PackageVersion Include="Moq" Version="4.20.72" />
    <PackageVersion Include="FluentAssertions" Version="6.11.0" />
    <PackageVersion Include="coverlet.collector" Version="6.0.0" />
  </ItemGroup>
</Project>