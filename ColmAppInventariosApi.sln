﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31815.197
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{106AE906-5075-410A-B941-912F811848EE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{250F283E-FE2F-4BBD-9E63-A2265B84E23F}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		CleanArchitectureApiTemplate.nuspec = CleanArchitectureApiTemplate.nuspec
		Directory.Build.props = Directory.Build.props
		Directory.Packages.props = Directory.Packages.props
		nuget.config = nuget.config
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ColmAppInventariosApi.Core", "src\ColmAppInventariosApi.Core\ColmAppInventariosApi.Core.csproj", "{AE355E70-2B3E-47E0-AF84-4617FFFFB554}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ColmAppInventariosApi.UseCases", "src\ColmAppInventariosApi.UseCases\ColmAppInventariosApi.UseCases.csproj", "{9546C158-2104-46FE-ACB6-459EC87FAF83}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ColmAppInventariosApi.Web", "src\ColmAppInventariosApi.Web\ColmAppInventariosApi.Web.csproj", "{B1131EB2-A5BA-45B5-AD71-AC0B6DA89336}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ColmAppInventariosApi.Infrastructure", "src\ColmAppInventariosApi.Infrastructure\ColmAppInventariosApi.Infrastructure.csproj", "{8B18766A-F8E3-47FF-87B3-359C022D810D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{8E1D8246-B4F2-4E58-B337-0DE06BE7CF47}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ColmAppInventariosApi.UnitTests", "tests\ColmAppInventariosApi.UnitTests\ColmAppInventariosApi.UnitTests.csproj", "{DC94F258-362C-4865-BEFD-D92797E43FC1}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{AE355E70-2B3E-47E0-AF84-4617FFFFB554}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AE355E70-2B3E-47E0-AF84-4617FFFFB554}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AE355E70-2B3E-47E0-AF84-4617FFFFB554}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AE355E70-2B3E-47E0-AF84-4617FFFFB554}.Release|Any CPU.Build.0 = Release|Any CPU
		{9546C158-2104-46FE-ACB6-459EC87FAF83}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9546C158-2104-46FE-ACB6-459EC87FAF83}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9546C158-2104-46FE-ACB6-459EC87FAF83}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9546C158-2104-46FE-ACB6-459EC87FAF83}.Release|Any CPU.Build.0 = Release|Any CPU
		{B1131EB2-A5BA-45B5-AD71-AC0B6DA89336}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B1131EB2-A5BA-45B5-AD71-AC0B6DA89336}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B1131EB2-A5BA-45B5-AD71-AC0B6DA89336}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B1131EB2-A5BA-45B5-AD71-AC0B6DA89336}.Release|Any CPU.Build.0 = Release|Any CPU
		{8B18766A-F8E3-47FF-87B3-359C022D810D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8B18766A-F8E3-47FF-87B3-359C022D810D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8B18766A-F8E3-47FF-87B3-359C022D810D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8B18766A-F8E3-47FF-87B3-359C022D810D}.Release|Any CPU.Build.0 = Release|Any CPU
		{DC94F258-362C-4865-BEFD-D92797E43FC1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DC94F258-362C-4865-BEFD-D92797E43FC1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DC94F258-362C-4865-BEFD-D92797E43FC1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DC94F258-362C-4865-BEFD-D92797E43FC1}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{AE355E70-2B3E-47E0-AF84-4617FFFFB554} = {106AE906-5075-410A-B941-912F811848EE}
		{9546C158-2104-46FE-ACB6-459EC87FAF83} = {106AE906-5075-410A-B941-912F811848EE}
		{B1131EB2-A5BA-45B5-AD71-AC0B6DA89336} = {106AE906-5075-410A-B941-912F811848EE}
		{8B18766A-F8E3-47FF-87B3-359C022D810D} = {106AE906-5075-410A-B941-912F811848EE}
		{DC94F258-362C-4865-BEFD-D92797E43FC1} = {8E1D8246-B4F2-4E58-B337-0DE06BE7CF47}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {B0F19343-8185-4A9F-9165-0EA8544BC925}
	EndGlobalSection
EndGlobal
