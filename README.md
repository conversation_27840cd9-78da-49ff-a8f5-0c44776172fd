# Colmapp - Inventory Management API (.NET 8)

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

**Author:** <PERSON>

## Overview

**Colmapp** is a mobile inventory management application specifically designed for small neighborhood stores (colmados) in the Dominican Republic. This .NET 8 Web API serves as the backend for the mobile app, enabling team members to visit assigned colmados and conduct daily inventory counts.

The application follows **Clean Architecture** principles, promoting separation of concerns, dependency inversion, and domain-driven design. This ensures the system is robust, maintainable, and can evolve with the business needs of Dominican colmados.

### Key Business Value

- **Streamlined Inventory Process**: Team members can efficiently conduct daily inventory counts at multiple colmados
- **Real-time Data Sharing**: Completed inventories are instantly shared with store owners (colmaderos)
- **Familiar Workflow**: Respects traditional colmado operations without forcing major changes to established practices
- **Mobile-First Design**: Built specifically for on-the-go inventory management

## Features

### Core Business Features
*   **Customer Management:** Manage colmado information including contact details and locations
*   **Product Catalog:** Centralized catalog with barcodes, short codes, and standardized product information
*   **Inventory Management:** Daily inventory tracking with real-time updates
*   **User Authentication:** Secure access using Kinde Auth for team members
*   **Mobile-Optimized API:** Designed specifically for mobile app consumption

### Technical Features
*   **Clean Architecture:** Follows standard Clean Architecture principles with distinct layers (Core, Infrastructure, UseCases, Web)
*   **Domain-Driven Design (DDD):** Focuses on inventory management domain with proper aggregates and entities
*   **.NET 8:** Built on the latest LTS version of .NET
*   **FastEndpoints:** Modern API endpoint framework for better performance and developer experience
*   **CQRS (Command Query Responsibility Segregation):** Organizes application logic into Commands and Queries using MediatR
*   **PostgreSQL with Neon:** Cloud-native PostgreSQL database for scalability and reliability
*   **Entity Framework Core:** Modern ORM for data access with PostgreSQL provider
*   **Kinde Authentication:** Enterprise-grade authentication and user management
*   **FluentValidation:** Robust input validation integrated into the request pipeline
*   **AutoMapper:** Object-to-object mapping for clean data transformations
*   **Dependency Injection:** Configured using Autofac for advanced DI scenarios
*   **Swagger/OpenAPI:** Comprehensive API documentation accessible via `/swagger`
*   **Health Checks:** Application and database health monitoring
*   **Unit & Integration Testing:** Comprehensive test coverage using xUnit

## Technology Stack

### Backend Framework
*   **.NET 8** - Latest LTS version of .NET
*   **ASP.NET Core 8** - Web framework for building APIs
*   **FastEndpoints** - Modern alternative to controllers for API endpoints

### Database & Storage
*   **PostgreSQL** - Primary database system
*   **Neon** - Cloud-native PostgreSQL hosting platform
*   **Entity Framework Core 8** - Object-relational mapping with PostgreSQL provider

### Authentication & Security
*   **Kinde Auth** - Enterprise authentication and user management platform
*   **JWT Tokens** - Secure API authentication

### Architecture & Patterns
*   **MediatR** - In-process messaging for CQRS implementation
*   **AutoMapper** - Object-to-object mapping
*   **FluentValidation** - Input validation framework
*   **Autofac** - Advanced dependency injection container
*   **Ardalis.Specification** - Specification pattern implementation

### Development & Testing
*   **Swagger/OpenAPI** - API documentation and testing
*   **xUnit** - Unit testing framework
*   **Serilog** - Structured logging
*   **Health Checks** - Application monitoring

## Architecture Overview

The Colmapp API follows Clean Architecture principles, ensuring separation of concerns and maintainability:

1.  **`Core` (Domain Layer):** Contains the inventory management domain model including:
    - **Entities**: Customer, Product, Inventory, InventoryProduct, User
    - **Aggregates**: Inventory management aggregates with proper business rules
    - **Value Objects**: Product codes, measurements, and domain-specific values
    - **Domain Events**: Inventory completion, product updates, etc.
    - **Specifications**: Complex query logic for inventory filtering and reporting
    - **Interfaces**: Repository contracts and domain services
    - **No external dependencies** - pure business logic

2.  **`Infrastructure` (Data & External Services):** Implements Core interfaces and handles:
    - **PostgreSQL Data Access**: Entity Framework Core with Neon cloud database
    - **Kinde Auth Integration**: User authentication and authorization
    - **External Services**: Email notifications, file storage, etc.
    - **AutoMapper Profiles**: Entity to DTO mappings
    - **Logging Services**: Structured logging with Serilog
    - **Depends only on Core layer**

3.  **`UseCases` (Application Layer):** Orchestrates business operations using CQRS:
    - **Commands**: Create/Update inventory, manage customers, product catalog operations
    - **Queries**: Retrieve inventories, search products, generate reports
    - **DTOs**: Data transfer objects for API contracts
    - **Validators**: FluentValidation rules for input validation
    - **Handlers**: MediatR command and query handlers
    - **Depends only on Core layer**

4.  **`Web` (Presentation Layer):** FastEndpoints-based API handling:
    - **Authentication**: Kinde Auth integration for secure access
    - **API Endpoints**: RESTful endpoints for mobile app consumption
    - **Global Exception Handling**: Consistent error responses
    - **Swagger Documentation**: Interactive API documentation
    - **Health Checks**: Application and database monitoring
    - **Depends on Infrastructure and UseCases layers**

**Dependency Flow**: `Web` → `Infrastructure` / `UseCases` → `Core`

## Folder Structure

```
├── src/
│   ├── ColmAppInventariosApi.Core/          # Domain Layer - Inventory Management Domain
│   │   ├── Aggregates/                      # Domain aggregates (Customer, Inventory, Product)
│   │   ├── Entities/                        # Domain entities and value objects
│   │   ├── Events/                          # Domain events (inventory completion, etc.)
│   │   ├── Interfaces/                      # Repository and service contracts
│   │   └── Specifications/                  # Query specifications
│   │
│   ├── ColmAppInventariosApi.Infrastructure/ # Infrastructure Layer - Data & External Services
│   │   ├── Data/                            # PostgreSQL data access with EF Core
│   │   ├── Auth/                            # Kinde authentication integration
│   │   ├── Services/                        # External service implementations
│   │   ├── Migrations/                      # Database migrations
│   │   └── Config/                          # AutoMapper profiles and configurations
│   │
│   ├── ColmAppInventariosApi.UseCases/      # Application Layer - Business Logic (CQRS)
│   │   ├── Customers/                       # Customer management use cases
│   │   ├── Inventory/                       # Inventory management use cases
│   │   ├── Products/                        # Product catalog use cases
│   │   ├── Users/                           # User management use cases
│   │   └── Common/                          # Shared DTOs, validators, behaviors
│   │
│   └── ColmAppInventariosApi.Web/           # Presentation Layer - FastEndpoints API
│       ├── Endpoints/                       # API endpoints organized by feature
│       ├── Auth/                            # Authentication middleware and configuration
│       ├── Config/                          # Swagger, database, and service configurations
│       └── Handlers/                        # Global exception and request handlers
│
├── tests/
│   ├── ColmAppInventariosApi.UnitTests/     # Unit tests for business logic
│   ├── ColmAppInventariosApi.IntegrationTests/ # Integration tests with database
│   └── ColmAppInventariosApi.FunctionalTests/  # End-to-end API tests
│
├── docs/                                    # Additional documentation
├── ColmAppInventariosApi.sln               # Visual Studio solution file
├── Directory.Build.props                   # MSBuild properties
├── Directory.Packages.props                # Central package management
├── global.json                             # .NET SDK version
└── README.md                               # This documentation
```

## Getting Started

### Prerequisites

1.  **.NET 8 SDK** - [Download here](https://dotnet.microsoft.com/download/dotnet/8.0)
2.  **PostgreSQL Database** - Either local installation or Neon cloud account
3.  **Kinde Auth Account** - For authentication services ([Sign up here](https://kinde.com))

### Environment Setup

1.  **Clone the Repository:**
    ```bash
    git clone <repository-url>
    cd ColmAppInventariosApi
    ```

2.  **Configure Database Connection:**
    *   Open `src/ColmAppInventariosApi.Web/appsettings.json`
    *   Update the PostgreSQL connection string:
        ```json
        {
          "ConnectionStrings": {
            "DefaultConnection": "Host=your-neon-host;Database=colmapp_db;Username=your-username;Password=your-password;SSL Mode=Require"
          }
        }
        ```

3.  **Configure Kinde Authentication:**
    *   Add your Kinde configuration to `appsettings.json`:
        ```json
        {
          "Kinde": {
            "Domain": "your-domain.kinde.com",
            "ClientId": "your-client-id",
            "ClientSecret": "your-client-secret"
          }
        }
        ```

4.  **Apply Database Migrations:**
    ```bash
    dotnet ef database update --project src/ColmAppInventariosApi.Infrastructure --startup-project src/ColmAppInventariosApi.Web
    ```

5.  **Run the Application:**
    ```bash
    cd src/ColmAppInventariosApi.Web
    dotnet run
    ```

6.  **Access the API:**
    *   The application will start on `https://localhost:7040` and `http://localhost:5091`
    *   Access Swagger UI at: `https://localhost:7040/swagger`
    *   Health checks available at: `https://localhost:7040/health`

## API Endpoints

The Colmapp API provides the following endpoints for inventory management:

### Customer Management
*   `GET /api/customers` - List all colmados
*   `GET /api/customers/{id}` - Get specific colmado details
*   `POST /api/customers` - Register new colmado
*   `PUT /api/customers/{id}` - Update colmado information
*   `DELETE /api/customers/{id}` - Remove colmado

### Product Catalog
*   `GET /api/products` - List products in catalog
*   `GET /api/products/{id}` - Get product details
*   `GET /api/products/search` - Search products by name, barcode, or short code
*   `POST /api/products` - Add new product to catalog
*   `PUT /api/products/{id}` - Update product information
*   `DELETE /api/products/{id}` - Remove product from catalog

### Inventory Management
*   `GET /api/inventory` - List inventories (with filtering)
*   `GET /api/inventory/{id}` - Get specific inventory details
*   `POST /api/inventory` - Create new inventory for a colmado
*   `PUT /api/inventory/{id}` - Update inventory
*   `POST /api/inventory/{id}/products` - Add products to inventory
*   `PUT /api/inventory/{id}/products/{productId}` - Update product quantities
*   `POST /api/inventory/{id}/complete` - Mark inventory as completed

### User Management
*   `GET /api/users/profile` - Get current user profile
*   `PUT /api/users/profile` - Update user profile

### System
*   `GET /health` - Application health check
*   `GET /health/database` - Database connectivity check

**Note:** All endpoints require authentication via Kinde Auth JWT tokens. Explore the complete API documentation in Swagger UI after running the project.

## Testing

The Colmapp API includes comprehensive testing to ensure reliability and maintainability:

### Test Structure
*   **Unit Tests** (`ColmAppInventariosApi.UnitTests`): Test business logic in isolation
    - Domain entity behavior and validation
    - Use case handlers (Commands and Queries)
    - Specification pattern implementations
    - Domain event handlers

*   **Integration Tests** (`ColmAppInventariosApi.IntegrationTests`): Test component interactions
    - Database operations with PostgreSQL
    - Repository implementations
    - AutoMapper configurations
    - External service integrations

*   **Functional Tests** (`ColmAppInventariosApi.FunctionalTests`): End-to-end API testing
    - Complete request/response cycles
    - Authentication flows with Kinde
    - API endpoint validation
    - Error handling scenarios

### Running Tests

```bash
# Run all tests
dotnet test

# Run specific test project
dotnet test tests/ColmAppInventariosApi.UnitTests

# Run tests with coverage
dotnet test --collect:"XPlat Code Coverage"

# Run tests in watch mode during development
dotnet watch test --project tests/ColmAppInventariosApi.UnitTests
```

### Test Database Setup

Integration and functional tests use a separate test database:
- Configured in `appsettings.Testing.json`
- Automatically created and seeded for each test run
- Cleaned up after test completion

## Domain Model

The Colmapp inventory management domain includes the following key entities:

### Core Entities

**Customer (Colmado)**
- Represents a neighborhood store
- Properties: Name, Email, Phone, Address
- Manages relationship with inventories

**Product Catalog**
- Centralized product information
- Properties: Name, Description, Barcode, Short Code, Measure Unit
- Provides standardized product data across all colmados

**Inventory**
- Represents a daily inventory session for a specific colmado
- Contains collection of inventory products
- Tracks creation and completion timestamps

**Inventory Product**
- Specific product within an inventory session
- Properties: Name, Description, Price, Stock Quantity, Measure Unit, Barcode
- Allows for colmado-specific product variations

**User**
- Team members who conduct inventories
- Integrated with Kinde authentication
- Manages access permissions and audit trails

### Business Rules

- Each inventory belongs to exactly one customer (colmado)
- Products can be added to inventory from the catalog or created on-the-fly
- Inventory sessions track both creation and completion times
- Short codes are managed in blocks to ensure uniqueness across the system
- All monetary values support up to 4 decimal places for precise pricing

## Development Workflow

### Adding New Features

1. **Domain First**: Start by defining entities and business rules in the Core layer
2. **Use Cases**: Create commands and queries in the UseCases layer
3. **Infrastructure**: Implement data access and external service integrations
4. **API Endpoints**: Add FastEndpoints in the Web layer
5. **Tests**: Write comprehensive unit and integration tests
6. **Documentation**: Update API documentation and README as needed

### Code Quality Standards

- Follow Clean Architecture principles
- Implement comprehensive unit test coverage (>80%)
- Use meaningful commit messages following conventional commits
- Ensure all tests pass before merging
- Follow C# coding conventions and use EditorConfig
- Document public APIs with XML comments

### Database Migrations

```bash
# Add new migration
dotnet ef migrations add MigrationName --project src/ColmAppInventariosApi.Infrastructure --startup-project src/ColmAppInventariosApi.Web

# Update database
dotnet ef database update --project src/ColmAppInventariosApi.Infrastructure --startup-project src/ColmAppInventariosApi.Web

# Remove last migration (if not applied)
dotnet ef migrations remove --project src/ColmAppInventariosApi.Infrastructure --startup-project src/ColmAppInventariosApi.Web
```

## Deployment

### Environment Configuration

The application supports multiple environments:
- **Development**: Local development with detailed logging
- **Staging**: Pre-production environment for testing
- **Production**: Live environment with optimized performance

### Production Deployment Checklist

- [ ] Configure production PostgreSQL database (Neon)
- [ ] Set up Kinde Auth production application
- [ ] Configure environment variables for sensitive data
- [ ] Enable HTTPS and security headers
- [ ] Set up health check monitoring
- [ ] Configure structured logging with appropriate sinks
- [ ] Implement database backup strategy
- [ ] Set up CI/CD pipeline for automated deployments

## Contributing

We welcome contributions to improve Colmapp! Please follow these guidelines:

1. **Fork the repository** and create a feature branch
2. **Follow the development workflow** outlined above
3. **Write tests** for new functionality
4. **Update documentation** as needed
5. **Submit a pull request** with a clear description of changes

### Reporting Issues

- Use GitHub Issues to report bugs or request features
- Provide detailed reproduction steps for bugs
- Include relevant logs and error messages
- Specify your environment (OS, .NET version, database version)

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**Colmapp** - Empowering Dominican colmados with modern inventory management while respecting traditional business practices.
