﻿using ColmAppInventariosApi.Core.Interfaces;
using Microsoft.AspNetCore.Diagnostics;

namespace ColmAppInventariosApi.Web.Handlers;

public class GlobalExceptionHandler : IExceptionHandler
{
  private readonly ILogger<GlobalExceptionHandler> _logger;
  private readonly ILogService _logService;

  public GlobalExceptionHandler(
    ILogger<GlobalExceptionHandler> logger,
    ILogService logService)
  {
    _logger = logger;
    _logService = logService;
  }

  public async ValueTask<bool> TryHandleAsync(HttpContext httpContext, Exception exception, CancellationToken cancellationToken)
  {
    // Keep standard logger for immediate system logging
    _logger.LogError(exception, "Handler Error.");

    // Use our custom LogService for comprehensive application logging
    string requestPath = httpContext.Request.Path;
    string httpMethod = httpContext.Request.Method;

    string errorMessage = $"Unhandled exception occurred. Method: {httpMethod}, Path: {requestPath}";
    await _logService.LogErrorAsync(errorMessage, exception);

    // Further error handling logic here...
    httpContext.Response.StatusCode = StatusCodes.Status500InternalServerError;

    return true;
  }
}
