using Ardalis.Result;
using ColmAppInventariosApi.UseCases.ProductsCatalog.Commands.Delete;
using FastEndpoints;
using MediatR;
using Microsoft.AspNetCore.Authorization;

namespace ColmAppInventariosApi.Web.Endpoints.ProductsCatalog;

[Authorize]
public class Delete(IMediator mediator) : EndpointWithoutRequest<Result>
{
  public override void Configure()
  {
    Delete("ProductsCatalog/{productsCatalogId:Guid}");
    Summary(s =>
    {
      s.Summary = "Elimina un producto del catálogo";
    });
  }

  public override async Task HandleAsync(CancellationToken cancellationToken)
  {
    var productsCatalogId = Route<Guid>("productsCatalogId");
    var result = await mediator.Send(new DeleteProductsCatalogCommand(productsCatalogId), cancellationToken);

    switch (result.Status)
    {
      case ResultStatus.Ok:
        Response = result;
        break;
      case ResultStatus.NotFound:
        ThrowError("Producto no encontrado");
        break;
      default:
        ThrowError(result.Errors.First().ToString());
        break;
    }
  }
}
