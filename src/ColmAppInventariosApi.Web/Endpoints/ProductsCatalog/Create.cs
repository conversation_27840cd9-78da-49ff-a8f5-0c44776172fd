using ColmAppInventariosApi.UseCases.ProductsCatalog.Commands.Create;
using ColmAppInventariosApi.UseCases.ProductsCatalog.Dtos;
using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Authorization;

namespace ColmAppInventariosApi.Web.Endpoints.ProductsCatalog;

[Authorize]
public class Create : BaseEndpoint<ProductsCatalogRequestDto, ProductsCatalogResponseDto>
{
  public Create(IMediator mediator, IValidator<ProductsCatalogRequestDto> validator)
      : base(mediator, validator)
  {
  }

  public override void Configure()
  {
    Post("ProductsCatalog");
    Summary(s =>
    {
      s.Summary = "Crea un nuevo producto en el catálogo";
      s.ExampleRequest = new ProductsCatalogRequestDto()
      {
        Name = "Coca Cola 2L",
        Description = "Bebida gaseosa Coca Cola de 2 litros",
        Barcode = 123456789,
        ShortCode = 1001,
        MeasureUnit = "Unidad"
      };
    });
  }

  protected override async Task HandleValidRequestAsync(
      ProductsCatalogRequestDto request,
      CancellationToken cancellationToken)
  {
    await SendCommandAsync(new CreateProductsCatalogCommand(request), cancellationToken);
  }
}
