﻿using ColmAppInventariosApi.Core.Abstract;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate.Models;
using ColmAppInventariosApi.UseCases.BankTransactions.Dtos;
using ColmAppInventariosApi.UseCases.BankTransactions.Queries.ListPaginated;
using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Authorization;

namespace ColmAppInventariosApi.Web.Endpoints.BankTransactions;

[Authorize]
public class List : BaseEndpoint<TransactionFilterParameters, PaginationResult<TransactionResponseDto>>
{
  public List(IMediator mediator, IValidator<TransactionFilterParameters> validator)
      : base(mediator, validator)
  {
  }
  public override void Configure()
  {
    Get("BankAccount/{bankAccountId:Guid}/Transaction");
    Summary(s =>
    {
      s.Summary = "Obtiene una lista paginada de transacciones para una cuenta bancaria con filtros opcionales";
      s.ExampleRequest = new TransactionFilterParameters()
      {
        BankAccountId = Guid.NewGuid(), // This will be taken from route
        PageNumber = 1,
        PageSize = 10,
        TransactionType = null,
        MinAmount = null,
        MaxAmount = null,
        StartDate = null,
        EndDate = null
      };
    });
  }
  protected override Task PreprocessRequestAsync(TransactionFilterParameters request, CancellationToken ct)
  {
    // Set BankAccountId from route parameter
    request.BankAccountId = Route<Guid>("bankAccountId");
    return Task.CompletedTask;
  }
  protected override async Task HandleValidRequestAsync(
      TransactionFilterParameters request,
      CancellationToken cancellationToken)
  {
    await SendCommandAsync(new ListPaginatedTransactionQuery(request), cancellationToken);
  }
}
