﻿using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate.Enums;
using ColmAppInventariosApi.UseCases.BankTransactions.Commands.Create;
using ColmAppInventariosApi.UseCases.BankTransactions.Dtos;
using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Authorization;

namespace ColmAppInventariosApi.Web.Endpoints.BankTransactions;

[Authorize]
public class Create : BaseEndpoint<TransactionRequestDto, TransactionResponseDto>
{
  public Create(IMediator mediator, IValidator<TransactionRequestDto> validator)
      : base(mediator, validator)
  {
  }
  public override void Configure()
  {
    Post("BankAccount/Transaction");
    Summary(s =>
    {
      s.Summary = "Crea una nueva transacción para una cuenta bancaria";
      s.ExampleRequest = new TransactionRequestDto()
      {
        BankAccountId = Guid.NewGuid(), // This will be taken from route
        Amount = 100.00m,
        Description = "Dep<PERSON>ito mensual",
        Type = TransactionType.Deposit
      };
    });
  }
  protected override async Task HandleValidRequestAsync(
      TransactionRequestDto request,
      CancellationToken cancellationToken)
  {
    await SendCommandAsync(new CreateTransactionCommand(request), cancellationToken);
  }
}
