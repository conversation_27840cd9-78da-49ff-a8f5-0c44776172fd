﻿using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate.Enums;
using ColmAppInventariosApi.UseCases.BankAccounts.Commands.Create;
using ColmAppInventariosApi.UseCases.BankAccounts.Dtos;
using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Authorization;

namespace ColmAppInventariosApi.Web.Endpoints.BankAccounts;

[Authorize]
public class Create : BaseEndpoint<BankAccountRequestDto, BankAccounResponseDto>
{
  public Create(IMediator mediator, IValidator<BankAccountRequestDto> validator)
      : base(mediator, validator)
  {
  }
  public override void Configure()
  {
    Post("BankAccount");
    Summary(s =>
    {
      s.Summary = "Crea una nueva cuenta bancaria";
      s.ExampleRequest = new BankAccountRequestDto()
      {
        AccountNumber = "**********",
        AccountHolderName = "John Doe",
        Balance = 1000.00m,
        AccountType = AccountType.Savings
      };
    });
  }
  protected override async Task HandleValidRequestAsync(
      BankAccountRequestDto request,
      CancellationToken cancellationToken)
  {
    await SendCommandAsync(new CreateBankAccountCommand(request), cancellationToken);
  }
}
