﻿using Ardalis.Result;
using ColmAppInventariosApi.UseCases.BankAccounts.Dtos;
using ColmAppInventariosApi.UseCases.BankAccounts.Queries.List;
using FastEndpoints;
using MediatR;
using Microsoft.AspNetCore.Authorization;

namespace ColmAppInventariosApi.Web.Endpoints.BankAccounts;

[Authorize]
public class List(IMediator _mediator)
    : EndpointWithoutRequest<Result<IEnumerable<BankAccounResponseDto>>>
{
  public override void Configure()
  {
    Get("BankAccount");
    Summary(s =>
    {
      s.Summary = "Obtiene todas las cuentas bancarias";
      s.Description = "Recupera una lista de todas las cuentas bancarias en el sistema";
    });
  }
  public override async Task HandleAsync(CancellationToken cancellationToken)
  {
    var result = await _mediator.Send(new ListBankAccountQuery(), cancellationToken);
    if (result.IsSuccess)
    {
      Response = result;
      return;
    }
    ThrowError(result.Errors.First().ToString());
  }
}
