﻿using Ardalis.Result;
using ColmAppInventariosApi.UseCases.BankAccounts.Commands.Delete;
using FastEndpoints;
using MediatR;
using Microsoft.AspNetCore.Authorization;

namespace ColmAppInventariosApi.Web.Endpoints.BankAccounts;

[Authorize]
public class Delete(IMediator _mediator)
    : EndpointWithoutRequest<Result>
{
  public override void Configure()
  {
    Delete("BankAccount/{bankAccountId:Guid}");
    Summary(s =>
    {
      s.Summary = "Elimina una cuenta bancaria";
      s.Description = "Elimina una cuenta bancaria por su ID";
    });
  }
  public override async Task HandleAsync(CancellationToken cancellationToken)
  {
    var bankAccountId = Route<Guid>("bankAccountId");
    var result = await _mediator.Send(new DeleteBankAccountCommand(bankAccountId), cancellationToken);
    if (result.IsSuccess)
    {
      Response = result;
      return;
    }
    if (result.Status == ResultStatus.NotFound)
    {
      ThrowError("Cuenta bancaria no encontrada");
      return;
    }
    ThrowError(result.Errors.First().ToString());
  }
}
