﻿using Ardalis.Result;
using ColmAppInventariosApi.UseCases.BankAccounts.Dtos;
using ColmAppInventariosApi.UseCases.BankAccounts.Queries.GetById;
using FastEndpoints;
using MediatR;
using Microsoft.AspNetCore.Authorization;

namespace ColmAppInventariosApi.Web.Endpoints.BankAccounts;

[Authorize]
public class GetById(IMediator mediator) : EndpointWithoutRequest<Result<BankAccounResponseDto>>
{
  public override void Configure()
  {
    Get("BankAccount/{bankAccountId:Guid}");
    Summary(s =>
    {
      s.Summary = "Obtiene una cuenta bancaria por ID";
    });
  }
  public override async Task HandleAsync(CancellationToken cancellationToken)
  {
    var bankAccountId = Route<Guid>("bankAccountId");
    var result = await mediator.Send(new GetByIdBankAccountQuery(bankAccountId), cancellationToken);
    if (result.IsSuccess)
    {
      Response = result;
      return;
    }
    if (result.Status == ResultStatus.NotFound)
    {
      ThrowError("Cuenta bancaria no encontrada");
      return;
    }
    ThrowError(result.Errors.First().ToString());
  }
}
