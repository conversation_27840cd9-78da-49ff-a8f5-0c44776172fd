﻿using Ardalis.Result;
using ColmAppInventariosApi.Core.Abstract;
using ColmAppInventariosApi.UseCases.BankAccounts.Dtos;
using ColmAppInventariosApi.UseCases.BankAccounts.ListPaginated;
using FastEndpoints;
using MediatR;
using Microsoft.AspNetCore.Authorization;

namespace ColmAppInventariosApi.Web.Endpoints.BankAccounts;

[Authorize]
public class ListPaginated(IMediator _mediator)
    : Endpoint<ListPaginatedBankAccountRequest, Result<PaginationResult<BankAccounResponseDto>>>
{
  public override void Configure()
  {
    Get("BankAccount/Paginated");
    Summary(s =>
    {
      s.Summary = "Obtiene lista paginada de cuentas bancarias";
      s.Description = "Recupera una lista paginada de cuentas bancarias con tamaño de página y número configurables";
      s.ExampleRequest = new ListPaginatedBankAccountRequest
      {
        PageNumber = 1,
        PageSize = 10
      };
    });
  }
  public override async Task HandleAsync(
      ListPaginatedBankAccountRequest request,
      CancellationToken cancellationToken)
  {
    var result = await _mediator.Send(
        new ListPaginatedBankAccountQuery(request.PageNumber, request.PageSize),
        cancellationToken);
    if (result.IsSuccess)
    {
      Response = result;
      return;
    }
    ThrowError(result.Errors.First().ToString());
  }
}
