﻿using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate.Enums;
using ColmAppInventariosApi.UseCases.BankAccounts.Commands.Update;
using ColmAppInventariosApi.UseCases.BankAccounts.Dtos;
using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Authorization;

namespace ColmAppInventariosApi.Web.Endpoints.BankAccounts;

[Authorize]
public class Update : BaseEndpoint<UpdateBankAccountDto, BankAccounResponseDto>
{
  public Update(IMediator mediator, IValidator<UpdateBankAccountDto> validator)
      : base(mediator, validator)
  {
  }
  public override void Configure()
  {
    Put("BankAccount");
    Summary(s =>
    {
      s.Summary = "Actualiza una cuenta bancaria existente";
      s.ExampleRequest = new UpdateBankAccountDto()
      {
        BankAccountId = Guid.NewGuid(),
        AccountNumber = "**********",
        AccountHolderName = "John Doe",
        Balance = 1000.00m,
        AccountType = AccountType.Savings
      };
    });
  }
  protected override async Task HandleValidRequestAsync(
      UpdateBankAccountDto request,
      CancellationToken cancellationToken)
  {
    await SendCommandAsync(new UpdateBankAccountCommand(request), cancellationToken);
  }
}
