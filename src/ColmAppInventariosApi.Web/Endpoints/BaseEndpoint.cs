﻿using Ardalis.Result;
using FastEndpoints;
using FluentValidation;
using FluentValidation.Results;
using MediatR;

namespace ColmAppInventariosApi.Web.Endpoints;

public abstract class BaseEndpoint<TRequest, TResponse> : Endpoint<TRequest, Result<TResponse>>
    where TRequest : class
    where TResponse : class
{
  protected readonly IMediator _mediator;
  protected readonly IValidator<TRequest> _validator;

  protected BaseEndpoint(IMediator mediator, IValidator<TRequest> validator)
  {
    _mediator = mediator;
    _validator = validator;
  }

  public override async Task HandleAsync(TRequest req, CancellationToken ct)
  {
    // Allow preprocessing of the request before validation (for route parameters, etc.)
    await PreprocessRequestAsync(req, ct);

    // Perform validation
    var validationResult = await _validator.ValidateAsync(req, ct);

    if (!validationResult.IsValid)
    {
      // Convert FluentValidation errors to Ardalis ValidationErrors
      var validationErrors = ConvertToArdalisValidationErrors(validationResult.Errors);

      // Create an Ardalis Result with validation errors
      var invalidResult = Result<TResponse>.Invalid(validationErrors);

      // Set the response directly using the Ardalis Result
      Response = invalidResult;

      // Return early
      return;
    }

    await HandleValidRequestAsync(req, ct);
  }

  /// <summary>
  /// Convert FluentValidation errors to Ardalis ValidationErrors
  /// </summary>
  private List<ValidationError> ConvertToArdalisValidationErrors(List<ValidationFailure> errors)
  {
    return errors.Select(error => new ValidationError(
        // Use PropertyName as the identifier
        identifier: FormatPropertyName(error.PropertyName),
        // Use ErrorMessage as is
        errorMessage: error.ErrorMessage,
        // Use ErrorCode if available, otherwise use a default
        errorCode: string.IsNullOrEmpty(error.ErrorCode) ? "ValidationError" : error.ErrorCode,
        // Use a default severity (Error = 0 in ValidationSeverity enum)
        severity: ValidationSeverity.Error
    )).ToList();
  }

  /// <summary>
  /// Preprocess the request before validation (e.g., set values from route parameters)
  /// </summary>
  protected virtual Task PreprocessRequestAsync(TRequest req, CancellationToken ct)
  {
    // Default implementation does nothing
    return Task.CompletedTask;
  }

  /// <summary>
  /// Handle the validated request
  /// </summary>
  protected abstract Task HandleValidRequestAsync(TRequest req, CancellationToken ct);

  /// <summary>
  /// Format property name for error messages
  /// </summary>
  private string FormatPropertyName(string propertyName)
  {
    // If the property name contains a dot (e.g. "User.Name"), take the last part
    if (propertyName.Contains('.'))
    {
      return propertyName.Split('.').Last();
    }
    return propertyName;
  }

  /// <summary>
  /// Send a command through the mediator and handle the result
  /// </summary>
  protected async Task<Result<TResponse>> SendCommandAsync<TCommand>(
      TCommand command,
      CancellationToken ct)
      where TCommand : IRequest<Result<TResponse>>
  {
    var result = await _mediator.Send(command, ct);

    if (result.IsSuccess)
    {
      Response = result;
      return result;
    }

    // For all error cases, just set the Response directly to maintain the Ardalis Result structure
    Response = result;

    return result;
  }
}
