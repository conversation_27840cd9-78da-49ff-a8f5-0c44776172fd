﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <Sdk Name="Microsoft.Build.CentralPackageVersions" Version="2.1.3" />

  <PropertyGroup>
    <PreserveCompilationContext>true</PreserveCompilationContext>
    <OutputType>Exe</OutputType>
    <WebProjectMode>true</WebProjectMode>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <SpaRoot>..\prevencionlavadoactivo.client</SpaRoot>
    <SpaProxyLaunchCommand>npm run dev</SpaProxyLaunchCommand>
    <SpaProxyServerUrl>https://localhost:5173</SpaProxyServerUrl>
  </PropertyGroup>

  <!-- Properties for NuGet packaging using the solution-level .nuspec -->
  <PropertyGroup>
    <!-- Tells dotnet pack to use this .nuspec file -->
    <NuspecFile>../../CleanArchitectureApiTemplate.nuspec</NuspecFile>
    <!-- Sets the base path for files referenced in the .nuspec, relative to the .nuspec file itself -->
    <NuspecBasePath>../../</NuspecBasePath>
    <!-- Explicitly enable packing for this project when targeted directly -->
    <IsPackable>true</IsPackable>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="clientapp\**" />
    <Content Remove="clientapp\**" />
    <EmbeddedResource Remove="clientapp\**" />
    <None Remove="clientapp\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Ardalis.ListStartupServices" />
    <PackageReference Include="Ardalis.Result" />
    <PackageReference Include="Ardalis.Result.AspNetCore" />
    <PackageReference Include="FastEndpoints" />
    <PackageReference Include="FastEndpoints.Swagger" />
    <PackageReference Include="MediatR" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" PrivateAssets="All" />
    <PackageReference Include="Serilog.AspNetCore" />
    <PackageReference Include="Serilog.Enrichers.Environment" />
    <PackageReference Include="Serilog.Enrichers.Thread" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Negotiate" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ColmAppInventariosApi.Infrastructure\ColmAppInventariosApi.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="logs\" />
  </ItemGroup>

</Project>
