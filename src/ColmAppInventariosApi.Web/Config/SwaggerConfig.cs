﻿using FastEndpoints.Swagger;

namespace ColmAppInventariosApi.Web.Config;

/// <summary>
/// Swagger Configuration for the API using FastEndpoints
/// </summary>
public static class SwaggerConfig
{
  /// <summary>
  /// Adds Swagger documentation services using FastEndpoints Swagger
  /// </summary>
  /// <param name="services">The service collection</param>
  /// <returns>The service collection for method chaining</returns>
  public static IServiceCollection AddSwaggerDocumentation(this IServiceCollection services)
  {
    // Register FastEndpoints Swagger services
    services.SwaggerDocument(options =>
    {
      // Basic API information
      options.ShortSchemaNames = true;
      options.DocumentSettings = document =>
      {
        document.Title = "Api .NET 8 Clean Arquitecture template";
        document.Version = "v1";
        document.Description = "API creada con los principios de la arquitectura limpia mediante FastEndpoints";
      };

      // Filter endpoints that shouldn't be displayed
      options.EndpointFilter = endpoint =>
      {
        // You can add logic here to exclude certain endpoints
        // Example: return !endpoint.EndpointTags.Contains("Internal");
        return true; // Include all endpoints by default
      };
    });

    return services;
  }
}
