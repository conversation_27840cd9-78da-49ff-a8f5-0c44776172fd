﻿using Ardalis.GuardClauses;
using Microsoft.EntityFrameworkCore;
using ColmAppInventariosApi.Infrastructure.Data;

namespace ColmAppInventariosApi.Web.Config;

public static class SqlConfig
{
  public static void AddSqlConnection(this IServiceCollection service, IConfiguration builder)
  {
    string? connectionString = builder.GetConnectionString("DefaultConnection");
    Guard.Against.Null(connectionString);
    service.AddDbContext<AppDbContext>(options => options.UseNpgsql(connectionString));
  }
}
