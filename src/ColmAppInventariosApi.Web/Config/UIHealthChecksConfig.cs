﻿using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace ColmAppInventariosApi.Web.Config;

public static class UIHealthChecksConfig
{
  public static Task WriteDetailedResponse(HttpContext context, HealthReport report)
  {
    context.Response.ContentType = "application/json";

    var response = new
    {
      status = report.Status.ToString(),
      duration = report.TotalDuration,
      components = report.Entries.Select(entry => new
      {
        name = entry.Key,
        status = entry.Value.Status.ToString(),
        description = entry.Value.Description,
        duration = entry.Value.Duration,
        error = entry.Value.Exception?.Message,
        data = entry.Value.Data
      })
    };

    return context.Response.WriteAsJsonAsync(response);
  }

  public static void UseHealthCheck(this WebApplication app)
  {
    app.MapHealthChecks("/health/detailed", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
    {
      ResponseWriter = WriteDetailedResponse,
      AllowCachingResponses = false
    });
  }
}
