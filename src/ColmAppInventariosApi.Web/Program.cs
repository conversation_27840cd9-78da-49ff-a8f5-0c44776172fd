﻿using Ardalis.ListStartupServices;
using Autofac;
using Autofac.Extensions.DependencyInjection;
using ColmAppInventariosApi.Core;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate.Events;
using ColmAppInventariosApi.Infrastructure;
using ColmAppInventariosApi.UseCases.BankAccounts.Validators;
using ColmAppInventariosApi.UseCases.Commons.Behaviors;
using ColmAppInventariosApi.Web.Config;
using ColmAppInventariosApi.Web.Handlers;
using FastEndpoints;
using FastEndpoints.Swagger;
using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Authentication.Negotiate;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Serilog;

//--------------------------------------------------
// INITIALIZATION AND LOGGING SETUP
//--------------------------------------------------
var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
builder.Host.UseSerilog((context, services, config) =>
    config.ReadFrom.Configuration(context.Configuration));

builder.Host.UseServiceProviderFactory(new AutofacServiceProviderFactory());

//--------------------------------------------------
// BASIC SERVICES
//--------------------------------------------------
builder.Services.Configure<CookiePolicyOptions>(options =>
{
  options.CheckConsentNeeded = context => true;
  options.MinimumSameSitePolicy = SameSiteMode.None;
});

// Required for LogService
builder.Services.AddHttpContextAccessor();
builder.Services.AddDistributedMemoryCache();
builder.Services.AddSession();

//--------------------------------------------------
// INFRASTRUCTURE SERVICES
//--------------------------------------------------
builder.Services.AddSqlConnection(builder.Configuration);

builder.Host.ConfigureContainer<ContainerBuilder>(containerBuilder =>
{
  containerBuilder.RegisterModule(new DefaultCoreModule());
  containerBuilder.RegisterModule(new AutofacInfrastructureModule(builder.Environment.IsDevelopment()));
});

//--------------------------------------------------
// HEALTH CHECKS
//--------------------------------------------------
builder.Services.AddHealthChecks()
    .AddCheck("API", () => HealthCheckResult.Healthy("API is running"));

//--------------------------------------------------
// AUTHENTICATION AND AUTHORIZATION
//--------------------------------------------------
builder.Services.AddAuthentication(NegotiateDefaults.AuthenticationScheme)
    .AddNegotiate();

builder.Services.AddAuthorization();

builder.Services.AddCors(opt =>
{
  opt.AddPolicy("CorsPolicy", builderCors => builderCors
      .AllowAnyHeader()
      .AllowAnyMethod()
      .WithOrigins(builder.Configuration.GetSection("CorsOrigins").Get<string[]>()!)
      .AllowCredentials());
});

//--------------------------------------------------
// VALIDATION AND MEDIATR
//--------------------------------------------------
// Register FluentValidation
builder.Services.AddValidatorsFromAssemblyContaining<BankAccountRequestDtoValidator>();

// Register MediatR with ValidationBehavior
builder.Services.AddMediatR(cfg =>
{
  // Register assemblies
  cfg.RegisterServicesFromAssembly(typeof(Program).Assembly);
  cfg.RegisterServicesFromAssembly(typeof(BankAccountRequestDtoValidator).Assembly);
  cfg.RegisterServicesFromAssembly(typeof(BankTransactionCreatedEvent).Assembly);

  // Add ValidationBehavior to the pipeline
  cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));
});

//--------------------------------------------------
// API AND ENDPOINTS
//--------------------------------------------------
builder.Services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());
builder.Services.AddFastEndpoints();
builder.Services.AddSwaggerDocumentation();
builder.Services.AddExceptionHandler<GlobalExceptionHandler>();

//--------------------------------------------------
// APPLICATION CONFIGURATION
//--------------------------------------------------
var app = builder.Build();

// Configure environment-specific middleware
if (app.Environment.IsDevelopment())
{
  app.UseDeveloperExceptionPage();
  app.UseShowAllServicesMiddleware(); // see https://github.com/ardalis/AspNetCoreStartupServices
}
else
{
  app.UseDefaultExceptionHandler(); // from FastEndpoints
  app.UseHsts();
}

//--------------------------------------------------
// MIDDLEWARE PIPELINE
//--------------------------------------------------
app.Use(async (context, next) =>
{
  context.Request.EnableBuffering();
  await next();
});

// Add Serilog request logging
app.UseSerilogRequestLogging();

app.UseStaticFiles();

//--------------------------------------------------
// SECURITY
//--------------------------------------------------
app.UseSession();
app.UseAuthentication();
app.UseAuthorization();

app.UseCors("CorsPolicy");
app.UseHttpsRedirection();

// FastEndpoints and API configuration
app.UseFastEndpoints(c =>
{
  c.Endpoints.RoutePrefix = "api";

  // Configure proper error response format
  c.Errors.StatusCode = StatusCodes.Status422UnprocessableEntity;
  c.Serializer.Options.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
});

app.UseSwaggerGen(); // FastEndpoints middleware
app.UseSwaggerUi(c =>
{
  c.CustomHeadContent = "<link rel=\"stylesheet\" type=\"text/css\" href=\"/swagger-ui/SwaggerDark.css\" />";
});

// Add detailed health check endpoint
app.UseHealthCheck();

app.Run();
