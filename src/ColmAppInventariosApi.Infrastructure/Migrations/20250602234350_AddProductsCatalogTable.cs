﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ColmAppInventariosApi.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddProductsCatalogTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "products_catalog",
                columns: table => new
                {
                    id = table.Column<string>(type: "text", nullable: false),
                    name = table.Column<string>(type: "varchar(100)", nullable: false),
                    description = table.Column<string>(type: "varchar(250)", nullable: false),
                    barcode = table.Column<int>(type: "integer", nullable: false),
                    short_code = table.Column<int>(type: "integer", nullable: false),
                    measure_unit = table.Column<string>(type: "varchar(100)", nullable: false),
                    deleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    deleted_date = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    created_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    created_by = table.Column<string>(type: "text", nullable: true),
                    deleted_by = table.Column<string>(type: "text", nullable: true),
                    updated_by = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_products_catalog", x => x.id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_products_catalog_barcode",
                table: "products_catalog",
                column: "barcode",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_products_catalog_short_code",
                table: "products_catalog",
                column: "short_code",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "products_catalog");
        }
    }
}
