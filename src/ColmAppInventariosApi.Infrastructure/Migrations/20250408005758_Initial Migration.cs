﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ColmAppInventariosApi.Infrastructure.Migrations;

  /// <inheritdoc />
  public partial class InitialMigration : Migration
  {
      /// <inheritdoc />
      protected override void Up(MigrationBuilder migrationBuilder)
      {
          migrationBuilder.CreateTable(
              name: "BankAccounts",
              columns: table => new
              {
                  Id = table.Column<Guid>(type: "uuid", nullable: false),
                  AccountNumber = table.Column<string>(type: "text", nullable: false),
                  AccountHolderName = table.Column<string>(type: "text", nullable: false),
                  Balance = table.Column<decimal>(type: "numeric", nullable: false),
                  AccountType = table.Column<int>(type: "integer", nullable: false),
                  Deleted = table.Column<bool>(type: "boolean", nullable: false),
                  DeletedDate = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                  CreatedDate = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                  UpdatedDate = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                  CreatedBy = table.Column<string>(type: "text", nullable: true),
                  DeletedBy = table.Column<string>(type: "text", nullable: true),
                  UpdatedBy = table.Column<string>(type: "text", nullable: true)
              },
              constraints: table =>
              {
                  table.PrimaryKey("PK_BankAccounts", x => x.Id);
              });

          migrationBuilder.CreateTable(
              name: "BankTransactions",
              columns: table => new
              {
                  Id = table.Column<Guid>(type: "uuid", nullable: false),
                  BankAccountId = table.Column<Guid>(type: "uuid", nullable: false),
                  Amount = table.Column<decimal>(type: "numeric", nullable: false),
                  Description = table.Column<string>(type: "text", nullable: false),
                  TransactionType = table.Column<int>(type: "integer", nullable: false),
                  Deleted = table.Column<bool>(type: "boolean", nullable: false),
                  DeletedDate = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                  CreatedDate = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                  UpdatedDate = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                  CreatedBy = table.Column<string>(type: "text", nullable: true),
                  DeletedBy = table.Column<string>(type: "text", nullable: true),
                  UpdatedBy = table.Column<string>(type: "text", nullable: true)
              },
              constraints: table =>
              {
                  table.PrimaryKey("PK_BankTransactions", x => x.Id);
                  table.ForeignKey(
                      name: "FK_BankTransactions_BankAccounts_BankAccountId",
                      column: x => x.BankAccountId,
                      principalTable: "BankAccounts",
                      principalColumn: "Id",
                      onDelete: ReferentialAction.Cascade);
              });

          migrationBuilder.CreateIndex(
              name: "IX_BankTransactions_BankAccountId",
              table: "BankTransactions",
              column: "BankAccountId");
      }

      /// <inheritdoc />
      protected override void Down(MigrationBuilder migrationBuilder)
      {
          migrationBuilder.DropTable(
              name: "BankTransactions");

          migrationBuilder.DropTable(
              name: "BankAccounts");
      }
  }
