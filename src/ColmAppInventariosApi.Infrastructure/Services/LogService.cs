﻿using System.Text;
using ColmAppInventariosApi.Core.Interfaces;
using Microsoft.AspNetCore.Http;
using Serilog;
using Serilog.Context;

namespace ColmAppInventariosApi.Infrastructure.Services;
public class LogService : ILogService
{
  private readonly IHttpContextAccessor _httpContextAccessor;
  private readonly ILogger _logger;

  public LogService(IHttpContextAccessor httpContextAccessor)
  {
    _httpContextAccessor = httpContextAccessor;
    _logger = Log.ForContext<LogService>();
  }

  public async Task LogErrorAsync(string message, Exception exception)
  {
    await EnrichLogContextAsync();
    _logger.Error(exception, "{Message}", message);
  }

  public async Task LogWarningAsync(string message)
  {
    await EnrichLogContextAsync();
    _logger.Warning("{Message}", message);
  }

  public async Task LogInfoAsync(string message)
  {
    await EnrichLogContextAsync();
    _logger.Information("{Message}", message);
  }

  public async Task LogSuccessAsync(string message)
  {
    await EnrichLogContextAsync();
    _logger.Information("[Success] {Message}", message);
  }

  private async Task EnrichLogContextAsync()
  {
    var httpContext = _httpContextAccessor.HttpContext;
    if (httpContext == null) return;

    // Add HTTP context information to the log context
    LogContext.PushProperty("HttpMethod", httpContext.Request?.Method);
    LogContext.PushProperty("Endpoint", httpContext.Request?.Path);
    LogContext.PushProperty("StatusCode", httpContext.Response?.StatusCode);
    LogContext.PushProperty("ClientIpAddress", httpContext.Connection?.RemoteIpAddress?.ToString());

    // Add request headers (simplified)
    try
    {
      var headers = httpContext.Request?.Headers;
      if (headers != null)
      {
        LogContext.PushProperty("UserAgent", headers["User-Agent"].ToString());
      }
    }
    catch
    {
      // Ignore header reading errors
    }

    // Add request body if needed
    if (ShouldCaptureRequestBody(httpContext))
    {
      try
      {
        var requestBody = await GetRequestBodyAsync();
        LogContext.PushProperty("RequestBody", requestBody);
      }
      catch
      {
        // Ignore body reading errors
      }
    }
  }

  private bool ShouldCaptureRequestBody(HttpContext httpContext)
  {
    // Only capture request body for certain content types or error status codes
    var contentType = httpContext.Request.ContentType;
    var isErrorStatus = httpContext.Response?.StatusCode >= 400;

    return isErrorStatus ||
           (contentType != null &&
           (contentType.Contains("application/json") ||
            contentType.Contains("application/xml") ||
            contentType.Contains("application/x-www-form-urlencoded")));
  }

  private async Task<string> GetRequestBodyAsync()
  {
    try
    {
      var httpContext = _httpContextAccessor.HttpContext;
      if (httpContext?.Request?.Body == null) return string.Empty;

      // Check if we've already cached the request body in HttpContext.Items
      const string requestBodyKey = "LogService_CachedRequestBody";
      if (httpContext.Items.TryGetValue(requestBodyKey, out var cachedBody))
      {
        return cachedBody as string ?? string.Empty;
      }

      // Enable buffering if not already enabled
      httpContext.Request.EnableBuffering();

      // Save the current position
      long originalPosition = httpContext.Request.Body.Position;

      // Reset to beginning
      httpContext.Request.Body.Position = 0;

      // Read the body
      using var streamReader = new StreamReader(
          httpContext.Request.Body,
          Encoding.UTF8,
          leaveOpen: true
      );

      var body = await streamReader.ReadToEndAsync();

      // Restore the original position
      httpContext.Request.Body.Position = originalPosition;

      // Cache the body for potential future reads
      httpContext.Items[requestBodyKey] = body;

      return body;
    }
    catch (Exception)
    {
      return "Unable to read request body";
    }
  }
}
