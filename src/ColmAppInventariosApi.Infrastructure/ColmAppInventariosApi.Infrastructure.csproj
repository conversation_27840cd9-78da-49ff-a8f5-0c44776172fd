﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <Sdk Name="Microsoft.Build.CentralPackageVersions" Version="2.1.3" />

  <ItemGroup>
    <PackageReference Include="Ardalis.SharedKernel" />
    <PackageReference Include="Ardalis.Specification.EntityFrameworkCore" />
    <PackageReference Include="Autofac" />
    <PackageReference Include="Autofac.Extensions.DependencyInjection" />
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="MailKit" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" PrivateAssets="all" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" />
    <PackageReference Include="SQLite" />
    <PackageReference Include="Serilog.AspNetCore" />
    <PackageReference Include="Serilog.Enrichers.Environment" />
    <PackageReference Include="Serilog.Enrichers.Thread" />
  </ItemGroup>
  
  <ItemGroup>
    <ProjectReference Include="..\ColmAppInventariosApi.Core\ColmAppInventariosApi.Core.csproj" />
    <ProjectReference Include="..\ColmAppInventariosApi.UseCases\ColmAppInventariosApi.UseCases.csproj" />
  </ItemGroup>
</Project>
