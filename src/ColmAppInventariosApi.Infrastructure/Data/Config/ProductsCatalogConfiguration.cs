using ColmAppInventariosApi.Core.Aggregates.ProductsCatalogAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ColmAppInventariosApi.Infrastructure.Data.Config;

public class ProductsCatalogConfiguration : IEntityTypeConfiguration<ProductsCatalog>
{
  public void Configure(EntityTypeBuilder<ProductsCatalog> builder)
  {
    builder.ToTable("products_catalog");

    builder.HasKey(p => p.Id);

    builder.Property(p => p.Id)
        .HasColumnName("id")
        .HasConversion(
            v => v.ToString(),
            v => Guid.Parse(v))
        .HasColumnType("text")
        .IsRequired();

    builder.Property(p => p.Name)
        .HasColumnName("name")
        .HasColumnType("varchar(100)")
        .IsRequired();

    builder.Property(p => p.Description)
        .HasColumnName("description")
        .HasColumnType("varchar(250)")
        .IsRequired();

    builder.Property(p => p.Barcode)
        .HasColumnName("barcode")
        .HasColumnType("integer")
        .IsRequired();

    builder.Property(p => p.ShortCode)
        .HasColumnName("short_code")
        .HasColumnType("integer")
        .IsRequired();

    builder.Property(p => p.MeasureUnit)
        .HasColumnName("measure_unit")
        .HasColumnType("varchar(100)")
        .IsRequired();

    builder.Property(p => p.CreatedDate)
        .HasColumnName("created_at")
        .HasColumnType("timestamp with time zone")
        .IsRequired();

    builder.Property(p => p.UpdatedDate)
        .HasColumnName("updated_at")
        .HasColumnType("timestamp with time zone");

    builder.Property(p => p.Deleted)
        .HasColumnName("deleted")
        .HasColumnType("boolean")
        .HasDefaultValue(false);

    builder.Property(p => p.DeletedDate)
        .HasColumnName("deleted_date")
        .HasColumnType("timestamp with time zone");

    builder.Property(p => p.CreatedBy)
        .HasColumnName("created_by")
        .HasColumnType("text");

    builder.Property(p => p.UpdatedBy)
        .HasColumnName("updated_by")
        .HasColumnType("text");

    builder.Property(p => p.DeletedBy)
        .HasColumnName("deleted_by")
        .HasColumnType("text");

    builder.HasIndex(p => p.Barcode)
        .IsUnique()
        .HasDatabaseName("IX_products_catalog_barcode");

    builder.HasIndex(p => p.ShortCode)
        .IsUnique()
        .HasDatabaseName("IX_products_catalog_short_code");
  }
}
