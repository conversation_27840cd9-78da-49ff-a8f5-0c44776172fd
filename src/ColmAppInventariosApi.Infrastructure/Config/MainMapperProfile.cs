﻿using AutoMapper;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate;
using ColmAppInventariosApi.Core.Aggregates.ProductsCatalogAggregate;
using ColmAppInventariosApi.UseCases.BankAccounts.Dtos;
using ColmAppInventariosApi.UseCases.BankTransactions.Dtos;
using ColmAppInventariosApi.UseCases.ProductsCatalog.Dtos;

namespace ColmAppInventariosApi.Infrastructure.Config;

public class MainMapperProfile : Profile
{
  public MainMapperProfile()
  {
    // Add Mapping profiles here
    CreateMap<BankAccount, BankAccountRequestDto>().ReverseMap();
    CreateMap<BankAccount, UpdateBankAccountDto>().ReverseMap();
    CreateMap<BankAccount, BankAccounResponseDto>().ReverseMap();

    CreateMap<BankTransaction, TransactionRequestDto>().ReverseMap();
    CreateMap<BankTransaction, TransactionResponseDto>().ReverseMap();

    CreateMap<ProductsCatalog, ProductsCatalogRequestDto>().ReverseMap();
    CreateMap<ProductsCatalog, UpdateProductsCatalogDto>().ReverseMap();
    CreateMap<ProductsCatalog, ProductsCatalogResponseDto>().ReverseMap();
  }
}
