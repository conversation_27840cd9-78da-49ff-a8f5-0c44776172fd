﻿using Ardalis.Result;
using Ardalis.SharedKernel;
using AutoMapper;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate;
using ColmAppInventariosApi.Core.Interfaces;
using ColmAppInventariosApi.UseCases.BankAccounts.Dtos;

namespace ColmAppInventariosApi.UseCases.BankAccounts.Queries.List;

public class ListBankAccountHandler : IQueryHandler<ListBankAccountQuery, Result<IEnumerable<BankAccounResponseDto>>>
{
  private readonly IReadRepository<BankAccount> _bankAccountReadRepository;
  private readonly IMapper _mapper;
  private readonly ILogService _logService;

  public ListBankAccountHandler(
      IReadRepository<BankAccount> bankAccountReadRepository,
      IMapper mapper,
      ILogService logService)
  {
    _bankAccountReadRepository = bankAccountReadRepository;
    _mapper = mapper;
    _logService = logService;
  }

  public async Task<Result<IEnumerable<BankAccounResponseDto>>> Handle(ListBankAccountQuery request, CancellationToken cancellationToken)
  {
    await _logService.LogInfoAsync("Recuperando todas las cuentas bancarias");

    try
    {
      IEnumerable<BankAccount> bankAccounts = await _bankAccountReadRepository.ListAsync(cancellationToken);
      var result = _mapper.Map<IEnumerable<BankAccounResponseDto>>(bankAccounts);
      int count = result.Count();

      await _logService.LogSuccessAsync($"Se recuperaron exitosamente {count} cuentas bancarias");
      return Result.Success(result);
    }
    catch (Exception ex)
    {
      await _logService.LogErrorAsync("Error al recuperar todas las cuentas bancarias", ex);
      return Result.Error(ex.Message);
    }
  }
}
