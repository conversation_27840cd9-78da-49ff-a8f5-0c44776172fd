﻿using Ardalis.Result;
using Ardalis.SharedKernel;
using AutoMapper;
using ColmAppInventariosApi.Core.Abstract;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate;
using ColmAppInventariosApi.Core.Interfaces;
using ColmAppInventariosApi.UseCases.BankAccounts.Dtos;
using ColmAppInventariosApi.UseCases.BankAccounts.ListPaginated;
using ColmAppInventariosApi.UseCases.Commons.Paginations;

namespace ColmAppInventariosApi.UseCases.BankAccounts.Queries.ListPaginated;

public class ListPaginatedBankAccountHandler : IQueryHandler<ListPaginatedBankAccountQuery, Result<PaginationResult<BankAccounResponseDto>>>
{
  private readonly IReadRepository<BankAccount> _bankAccountReadRepository;
  private readonly IMapper _mapper;
  private readonly ILogService _logService;

  public ListPaginatedBankAccountHandler(
      IReadRepository<BankAccount> bankAccountReadRepository,
      IMapper mapper,
      ILogService logService)
  {
    _bankAccountReadRepository = bankAccountReadRepository;
    _mapper = mapper;
    _logService = logService;
  }

  public async Task<Result<PaginationResult<BankAccounResponseDto>>> Handle(ListPaginatedBankAccountQuery request, CancellationToken cancellationToken)
  {
    await _logService.LogInfoAsync($"Recuperando cuentas bancarias paginadas (Página: {request.PageNumber}, Tamaño: {request.PageSize})");

    try
    {
      var pagedResult = await _bankAccountReadRepository.GetPagedAsync<BankAccount, BankAccounResponseDto>(
          request.PageNumber,
          request.PageSize,
          _mapper,
          cancellationToken);

      await _logService.LogSuccessAsync($"Cuentas bancarias paginadas recuperadas exitosamente " +
                                     $"(Página: {request.PageNumber}, Tamaño: {request.PageSize}, " +
                                     $"Total: {pagedResult.Items.Count()}/{pagedResult.TotalCount})");

      return pagedResult.ToSuccessResult();
    }
    catch (Exception ex)
    {
      await _logService.LogErrorAsync($"Error al recuperar cuentas bancarias paginadas (Página: {request.PageNumber}, Tamaño: {request.PageSize})", ex);
      return Result.Error(ex.Message);
    }
  }
}
