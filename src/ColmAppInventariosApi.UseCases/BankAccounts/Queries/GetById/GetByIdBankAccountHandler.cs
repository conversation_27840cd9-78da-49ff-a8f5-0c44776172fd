﻿using Ardalis.Result;
using Ardalis.SharedKernel;
using AutoMapper;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate;
using ColmAppInventariosApi.Core.Interfaces;
using ColmAppInventariosApi.UseCases.BankAccounts.Dtos;

namespace ColmAppInventariosApi.UseCases.BankAccounts.Queries.GetById;

public class GetByIdBankAccountHandler : IQueryHandler<GetByIdBankAccountQuery, Result<BankAccounResponseDto>>
{
  private readonly IReadRepository<BankAccount> _bankAccountReadRepository;
  private readonly IMapper _mapper;
  private readonly ILogService _logService;

  public GetByIdBankAccountHandler(
      IReadRepository<BankAccount> bankAccountReadRepository,
      IMapper mapper,
      ILogService logService)
  {
    _bankAccountReadRepository = bankAccountReadRepository;
    _mapper = mapper;
    _logService = logService;
  }

  public async Task<Result<BankAccounResponseDto>> Handle(GetByIdBankAccountQuery request, CancellationToken cancellationToken)
  {
    await _logService.LogInfoAsync($"Recuperando cuenta bancaria por ID: {request.BankAccountId}");

    try
    {
      var bankAccount = await _bankAccountReadRepository.GetByIdAsync(request.BankAccountId, cancellationToken);

      if (bankAccount is null)
      {
        await _logService.LogWarningAsync($"Cuenta bancaria ID: {request.BankAccountId} no encontrada");
        return Result.NotFound("Cuenta bancaria no encontrada");
      }

      var result = _mapper.Map<BankAccounResponseDto>(bankAccount);
      await _logService.LogSuccessAsync($"Cuenta bancaria recuperada exitosamente ID: {bankAccount.Id}, Nombre: {bankAccount.AccountHolderName}");

      return Result.Success(result);
    }
    catch (Exception ex)
    {
      await _logService.LogErrorAsync($"Error al recuperar cuenta bancaria ID: {request.BankAccountId}", ex);
      return Result.Error(ex.Message);
    }
  }
}
