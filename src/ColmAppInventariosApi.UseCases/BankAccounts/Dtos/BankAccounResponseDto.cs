﻿using ColmAppInventariosApi.Core.Abstract;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate.Enums;

namespace ColmAppInventariosApi.UseCases.BankAccounts.Dtos;
public class BankAccounResponseDto : BaseResponseDto
{
  public string AccountNumber { get; set; } = string.Empty;
  public string AccountHolderName { get; set; } = string.Empty;
  public decimal Balance { get; set; }
  public AccountType AccountType { get; set; }
}
