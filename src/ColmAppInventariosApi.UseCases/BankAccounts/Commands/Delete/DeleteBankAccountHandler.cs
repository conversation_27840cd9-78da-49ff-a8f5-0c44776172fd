﻿using Ardalis.Result;
using Ardalis.SharedKernel;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate;
using ColmAppInventariosApi.Core.Interfaces;

namespace ColmAppInventariosApi.UseCases.BankAccounts.Commands.Delete;

public class DeleteBankAccountHandler : ICommandHandler<DeleteBankAccountCommand, Result>
{
  private readonly IRepository<BankAccount> _bankAccountReadRepository;
  private readonly ILogService _logService;

  public DeleteBankAccountHandler(
      IRepository<BankAccount> bankAccountReadRepository,
      ILogService logService)
  {
    _bankAccountReadRepository = bankAccountReadRepository;
    _logService = logService;
  }

  public async Task<Result> Handle(DeleteBankAccountCommand request, CancellationToken cancellationToken)
  {
    await _logService.LogInfoAsync($"Intentando eliminar cuenta bancaria ID: {request.BankAccountId}");

    try
    {
      var bankAccount = await _bankAccountReadRepository.GetByIdAsync(request.BankAccountId, cancellationToken);

      if (bankAccount is null)
      {
        await _logService.LogWarningAsync($"Eliminación fallida: Cuenta bancaria ID: {request.BankAccountId} no encontrada");
        return Result.NotFound("Cuenta bancaria no encontrada");
      }

      await _bankAccountReadRepository.DeleteAsync(bankAccount, cancellationToken);
      await _logService.LogSuccessAsync($"Cuenta bancaria eliminada exitosamente ID: {request.BankAccountId}, Nombre: {bankAccount.AccountHolderName}");

      return Result.Success();
    }
    catch (Exception ex)
    {
      await _logService.LogErrorAsync($"Error al eliminar cuenta bancaria ID: {request.BankAccountId}", ex);
      return Result.Error(ex.Message);
    }
  }
}
