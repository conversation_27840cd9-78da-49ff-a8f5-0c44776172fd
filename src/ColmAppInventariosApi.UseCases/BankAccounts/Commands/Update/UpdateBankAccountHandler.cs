﻿using Ardalis.Result;
using Ardalis.SharedKernel;
using AutoMapper;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate;
using ColmAppInventariosApi.Core.Interfaces;
using ColmAppInventariosApi.UseCases.BankAccounts.Dtos;

namespace ColmAppInventariosApi.UseCases.BankAccounts.Commands.Update;

public class UpdateBankAccountHandler : ICommandHandler<UpdateBankAccountCommand, Result<BankAccounResponseDto>>
{
  private readonly IRepository<BankAccount> _repository;
  private readonly IMapper _mapper;
  private readonly ILogService _logService;

  public UpdateBankAccountHandler(
      IRepository<BankAccount> repository,
      IMapper mapper,
      ILogService logService)
  {
    _repository = repository;
    _mapper = mapper;
    _logService = logService;
  }

  public async Task<Result<BankAccounResponseDto>> Handle(UpdateBankAccountCommand request, CancellationToken cancellationToken)
  {
    await _logService.LogInfoAsync($"Intentando actualizar cuenta bancaria ID: {request.UpdateDto.BankAccountId}");

    try
    {
      var bankAccount = await _repository.GetByIdAsync(request.UpdateDto.BankAccountId, cancellationToken);

      if (bankAccount is null)
      {
        await _logService.LogWarningAsync($"Actualización fallida: Cuenta bancaria ID: {request.UpdateDto.BankAccountId} no encontrada");
        return Result.NotFound("Cuenta bancaria no encontrada");
      }

      var oldName = bankAccount.AccountHolderName;
      _mapper.Map(request.UpdateDto, bankAccount);
      await _repository.UpdateAsync(bankAccount, cancellationToken);
      var bankAccountResponse = _mapper.Map<BankAccounResponseDto>(bankAccount);

      await _logService.LogSuccessAsync($"Cuenta bancaria actualizada exitosamente ID: {bankAccount.Id} de '{oldName}' a '{bankAccount.AccountHolderName}'");
      return Result.Success(bankAccountResponse);
    }
    catch (Exception ex)
    {
      await _logService.LogErrorAsync($"Error al actualizar cuenta bancaria ID: {request.UpdateDto.BankAccountId}", ex);
      return Result.Error(ex.Message);
    }
  }
}
