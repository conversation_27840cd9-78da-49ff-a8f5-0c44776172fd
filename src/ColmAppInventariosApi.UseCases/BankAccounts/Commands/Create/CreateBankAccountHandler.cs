﻿using Ardalis.Result;
using Ardalis.SharedKernel;
using AutoMapper;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate;
using ColmAppInventariosApi.Core.Interfaces;
using ColmAppInventariosApi.UseCases.BankAccounts.Dtos;

namespace ColmAppInventariosApi.UseCases.BankAccounts.Commands.Create;

public class CreateBankAccountHandler : ICommandHandler<CreateBankAccountCommand, Result<BankAccounResponseDto>>
{
  private readonly IRepository<BankAccount> _repository;
  private readonly IMapper _mapper;
  private readonly ILogService _logService;

  public CreateBankAccountHandler(
      IRepository<BankAccount> repository,
      IMapper mapper,
      ILogService logService)
  {
    _repository = repository;
    _mapper = mapper;
    _logService = logService;
  }

  public async Task<Result<BankAccounResponseDto>> Handle(CreateBankAccountCommand request, CancellationToken cancellationToken)
  {
    await _logService.LogInfoAsync($"Creando nueva cuenta bancaria con nombre: {request.Dto.AccountHolderName}");

    try
    {
      var bankAccountEntity = _mapper.Map<BankAccount>(request.Dto);
      await _repository.AddAsync(bankAccountEntity, cancellationToken);
      var bankAccountResponse = _mapper.Map<BankAccounResponseDto>(bankAccountEntity);

      await _logService.LogSuccessAsync($"Cuenta bancaria creada exitosamente ID: {bankAccountEntity.Id}, Nombre: {bankAccountEntity.AccountHolderName}");
      return Result.Success(bankAccountResponse);
    }
    catch (Exception ex)
    {
      await _logService.LogErrorAsync($"Error al crear cuenta bancaria: {request.Dto.AccountHolderName}", ex);
      return Result.Error(ex.Message);
    }
  }
}
