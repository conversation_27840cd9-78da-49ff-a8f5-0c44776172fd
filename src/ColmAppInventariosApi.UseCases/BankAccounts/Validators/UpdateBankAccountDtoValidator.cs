﻿using Ardalis.SharedKernel;
using FluentValidation;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate.Specs;
using ColmAppInventariosApi.UseCases.BankAccounts.Dtos;

namespace ColmAppInventariosApi.UseCases.BankAccounts.Validators;

public class UpdateBankAccountDtoValidator : AbstractValidator<UpdateBankAccountDto>
{
  private readonly IRepository<BankAccount> _bankAccountRepository;

  public UpdateBankAccountDtoValidator(IRepository<BankAccount> bankAccountRepository)
  {
    _bankAccountRepository = bankAccountRepository;

    RuleFor(x => x.BankAccountId)
        .NotEqual(Guid.Empty).WithMessage("El ID de la cuenta bancaria es obligatorio")
        .MustAsync(ExistInDatabase).WithMessage("Cuenta bancaria no encontrada");

    RuleFor(x => x)
        .MustAsync(HaveUniqueAccountNumber).WithMessage("El número de cuenta ya existe");

    RuleFor(x => x.AccountNumber)
        .NotEmpty().WithMessage("El número de cuenta es obligatorio");

    RuleFor(x => x.AccountHolderName)
        .NotEmpty().WithMessage("El nombre del titular de la cuenta es obligatorio");

    RuleFor(x => x.Balance)
        .GreaterThanOrEqualTo(0).WithMessage("El saldo no puede ser negativo");

    RuleFor(x => x.AccountType)
        .IsInEnum().WithMessage("Tipo de cuenta inválido");
  }

  private async Task<bool> ExistInDatabase(Guid bankAccountId, CancellationToken cancellationToken)
  {
    var existingAccount = await _bankAccountRepository.GetByIdAsync(bankAccountId, cancellationToken);
    return existingAccount != null;
  }

  private async Task<bool> HaveUniqueAccountNumber(UpdateBankAccountDto dto, CancellationToken cancellationToken)
  {
    var spec = new GetBankAccountByNumberSpec(dto.AccountNumber);
    var existingAccount = await _bankAccountRepository.FirstOrDefaultAsync(spec, cancellationToken);
    return existingAccount == null || existingAccount.Id == dto.BankAccountId;
  }
}
