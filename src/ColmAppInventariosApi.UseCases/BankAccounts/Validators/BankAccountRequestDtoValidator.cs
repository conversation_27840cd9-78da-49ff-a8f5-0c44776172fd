﻿using Ardalis.SharedKernel;
using FluentValidation;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate.Specs;
using ColmAppInventariosApi.UseCases.BankAccounts.Dtos;

namespace ColmAppInventariosApi.UseCases.BankAccounts.Validators;

public class BankAccountRequestDtoValidator : AbstractValidator<BankAccountRequestDto>
{
  private readonly IRepository<BankAccount> _bankAccountRepository;

  public BankAccountRequestDtoValidator(IRepository<BankAccount> bankAccountRepository)
  {
    _bankAccountRepository = bankAccountRepository;

    RuleFor(x => x.AccountNumber)
        .NotEmpty().WithMessage("El número de cuenta es obligatorio")
        .MustAsync(BeUniqueAccountNumber).WithMessage("El número de cuenta ya existe");

    RuleFor(x => x.AccountHolderName)
        .NotEmpty().WithMessage("El nombre del titular de la cuenta es obligatorio");

    RuleFor(x => x.Balance)
        .GreaterThanOrEqualTo(0).WithMessage("El saldo inicial no puede ser negativo");

    RuleFor(x => x.AccountType)
        .IsInEnum().WithMessage("Tipo de cuenta inválido");
  }

  private async Task<bool> BeUniqueAccountNumber(string accountNumber, CancellationToken cancellationToken)
  {
    var spec = new GetBankAccountByNumberSpec(accountNumber);
    var existingAccount = await _bankAccountRepository.FirstOrDefaultAsync(spec, cancellationToken);
    return existingAccount == null;
  }
}
