using Ardalis.Result;
using Ardalis.SharedKernel;
using AutoMapper;
using ColmAppInventariosApi.Core.Aggregates.ProductsCatalogAggregate;
using ColmAppInventariosApi.Core.Interfaces;
using ColmAppInventariosApi.UseCases.ProductsCatalog.Dtos;

namespace ColmAppInventariosApi.UseCases.ProductsCatalog.Queries.GetById;

public class GetByIdProductsCatalogHandler : IQueryHandler<GetByIdProductsCatalogQuery, Result<ProductsCatalogResponseDto>>
{
  private readonly IReadRepository<Core.Aggregates.ProductsCatalogAggregate.ProductsCatalog> _productsCatalogReadRepository;
  private readonly IMapper _mapper;
  private readonly ILogService _logService;

  public GetByIdProductsCatalogHandler(
      IReadRepository<ProductsCatalog> productsCatalogReadRepository,
      IMapper mapper,
      ILogService logService)
  {
    _productsCatalogReadRepository = productsCatalogReadRepository;
    _mapper = mapper;
    _logService = logService;
  }

  public async Task<Result<ProductsCatalogResponseDto>> Handle(GetByIdProductsCatalogQuery request, CancellationToken cancellationToken)
  {
    await _logService.LogInfoAsync($"Recuperando producto por ID: {request.ProductsCatalogId}");

    try
    {
      var productsCatalog = await _productsCatalogReadRepository.GetByIdAsync(request.ProductsCatalogId, cancellationToken);

      if (productsCatalog is null)
      {
        await _logService.LogWarningAsync($"Producto ID: {request.ProductsCatalogId} no encontrado");
        return Result.NotFound("Producto no encontrado");
      }

      var result = _mapper.Map<ProductsCatalogResponseDto>(productsCatalog);
      await _logService.LogSuccessAsync($"Producto recuperado exitosamente ID: {productsCatalog.Id}, Nombre: {productsCatalog.Name}");

      return Result.Success(result);
    }
    catch (Exception ex)
    {
      await _logService.LogErrorAsync($"Error al recuperar producto ID: {request.ProductsCatalogId}", ex);
      return Result.Error(ex.Message);
    }
  }
}
