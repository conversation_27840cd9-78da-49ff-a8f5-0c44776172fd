using Ardalis.Result;
using Ardalis.SharedKernel;
using AutoMapper;
using ColmAppInventariosApi.Core.Aggregates.ProductsCatalogAggregate;
using ColmAppInventariosApi.Core.Interfaces;
using ColmAppInventariosApi.UseCases.ProductsCatalog.Dtos;

namespace ColmAppInventariosApi.UseCases.ProductsCatalog.Queries.List;

public class ListProductsCatalogHandler : IQueryHandler<ListProductsCatalogQuery, Result<IEnumerable<ProductsCatalogResponseDto>>>
{
  private readonly IReadRepository<ProductsCatalog> _productsCatalogReadRepository;
  private readonly IMapper _mapper;
  private readonly ILogService _logService;

  public ListProductsCatalogHandler(
      IReadRepository<ProductsCatalog> productsCatalogReadRepository,
      IMapper mapper,
      ILogService logService)
  {
    _productsCatalogReadRepository = productsCatalogReadRepository;
    _mapper = mapper;
    _logService = logService;
  }

  public async Task<Result<IEnumerable<ProductsCatalogResponseDto>>> Handle(ListProductsCatalogQuery request, CancellationToken cancellationToken)
  {
    await _logService.LogInfoAsync("Recuperando todos los productos del catálogo");

    try
    {
      IEnumerable<ProductsCatalog> productsCatalog = await _productsCatalogReadRepository.ListAsync(cancellationToken);
      var result = _mapper.Map<IEnumerable<ProductsCatalogResponseDto>>(productsCatalog);
      int count = result.Count();

      await _logService.LogSuccessAsync($"Se recuperaron exitosamente {count} productos del catálogo");
      return Result.Success(result);
    }
    catch (Exception ex)
    {
      await _logService.LogErrorAsync("Error al recuperar todos los productos del catálogo", ex);
      return Result.Error(ex.Message);
    }
  }
}
