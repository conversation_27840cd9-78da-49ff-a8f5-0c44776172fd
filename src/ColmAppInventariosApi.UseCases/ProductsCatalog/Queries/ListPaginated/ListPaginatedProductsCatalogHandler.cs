using Ardalis.Result;
using Ardalis.SharedKernel;
using AutoMapper;
using ColmAppInventariosApi.Core.Abstract;
using ColmAppInventariosApi.Core.Aggregates.ProductsCatalogAggregate;
using ColmAppInventariosApi.Core.Interfaces;
using ColmAppInventariosApi.UseCases.ProductsCatalog.Dtos;
using ColmAppInventariosApi.UseCases.Commons.Paginations;

namespace ColmAppInventariosApi.UseCases.ProductsCatalog.Queries.ListPaginated;

public class ListPaginatedProductsCatalogHandler : IQueryHandler<ListPaginatedProductsCatalogQuery, Result<PaginationResult<ProductsCatalogResponseDto>>>
{
  private readonly IReadRepository<Core.Aggregates.ProductsCatalogAggregate.ProductsCatalog> _productsCatalogReadRepository;
  private readonly IMapper _mapper;
  private readonly ILogService _logService;

  public ListPaginatedProductsCatalogHandler(
      IReadRepository<Core.Aggregates.ProductsCatalogAggregate.ProductsCatalog> productsCatalogReadRepository,
      IMapper mapper,
      ILogService logService)
  {
    _productsCatalogReadRepository = productsCatalogReadRepository;
    _mapper = mapper;
    _logService = logService;
  }

  public async Task<Result<PaginationResult<ProductsCatalogResponseDto>>> Handle(ListPaginatedProductsCatalogQuery request, CancellationToken cancellationToken)
  {
    await _logService.LogInfoAsync($"Recuperando productos del catálogo paginados (Página: {request.PageNumber}, Tamaño: {request.PageSize})");

    try
    {
      var pagedResult = await _productsCatalogReadRepository.GetPagedAsync<Core.Aggregates.ProductsCatalogAggregate.ProductsCatalog, ProductsCatalogResponseDto>(
          request.PageNumber,
          request.PageSize,
          _mapper,
          cancellationToken);

      await _logService.LogSuccessAsync($"Productos del catálogo paginados recuperados exitosamente " +
                                     $"(Página: {request.PageNumber}, Tamaño: {request.PageSize}, " +
                                     $"Total: {pagedResult.Items.Count}/{pagedResult.TotalCount})");

      return pagedResult.ToSuccessResult();
    }
    catch (Exception ex)
    {
      await _logService.LogErrorAsync($"Error al recuperar productos del catálogo paginados (Página: {request.PageNumber}, Tamaño: {request.PageSize})", ex);
      return Result.Error(ex.Message);
    }
  }
}
