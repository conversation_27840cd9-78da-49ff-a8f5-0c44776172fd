using Ardalis.SharedKernel;
using ColmAppInventariosApi.Core.Aggregates.ProductsCatalogAggregate;
using ColmAppInventariosApi.UseCases.ProductsCatalog.Dtos;
using FluentValidation;

namespace ColmAppInventariosApi.UseCases.ProductsCatalog.Validators;

public class UpdateProductsCatalogDtoValidator : AbstractValidator<UpdateProductsCatalogDto>
{
  private readonly IRepository<ProductsCatalog> _productsCatalogRepository;

  public UpdateProductsCatalogDtoValidator(IRepository<ProductsCatalog> productsCatalogRepository)
  {
    _productsCatalogRepository = productsCatalogRepository;

    RuleFor(x => x.ProductsCatalogId)
        .NotEqual(Guid.Empty).WithMessage("El ID del producto es obligatorio")
        .MustAsync(ExistInDatabaseAsync).WithMessage("Producto no encontrado");

    RuleFor(x => x.Name)
        .NotEmpty().WithMessage("El nombre del producto es obligatorio")
        .MaximumLength(100).WithMessage("El nombre del producto no puede exceder 100 caracteres");

    RuleFor(x => x.Description)
        .NotEmpty().WithMessage("La descripción del producto es obligatoria")
        .MaximumLength(250).WithMessage("La descripción del producto no puede exceder 250 caracteres");

    RuleFor(x => x.Barcode)
        .GreaterThan(0).WithMessage("El código de barras debe ser mayor que 0")
        .MustAsync(BeUniqueBarcodeForUpdateAsync).WithMessage("El código de barras ya existe");

    RuleFor(x => x.ShortCode)
        .GreaterThan(0).WithMessage("El código corto debe ser mayor que 0")
        .MustAsync(BeUniqueShortCodeForUpdateAsync).WithMessage("El código corto ya existe");

    RuleFor(x => x.MeasureUnit)
        .NotEmpty().WithMessage("La unidad de medida es obligatoria")
        .MaximumLength(100).WithMessage("La unidad de medida no puede exceder 100 caracteres");
  }

  private async Task<bool> ExistInDatabaseAsync(Guid productsCatalogId, CancellationToken cancellationToken)
  {
    var product = await _productsCatalogRepository.GetByIdAsync(productsCatalogId, cancellationToken);
    return product != null;
  }

  private async Task<bool> BeUniqueBarcodeForUpdateAsync(UpdateProductsCatalogDto dto, int barcode, CancellationToken cancellationToken)
  {
    var existingProduct = await _productsCatalogRepository.FirstOrDefaultAsync(
        new Core.Aggregates.ProductsCatalogAggregate.Specs.GetProductsCatalogByBarcodeSpec(barcode), 
        cancellationToken);
    return existingProduct == null || existingProduct.Id == dto.ProductsCatalogId;
  }

  private async Task<bool> BeUniqueShortCodeForUpdateAsync(UpdateProductsCatalogDto dto, int shortCode, CancellationToken cancellationToken)
  {
    var existingProduct = await _productsCatalogRepository.FirstOrDefaultAsync(
        new Core.Aggregates.ProductsCatalogAggregate.Specs.GetProductsCatalogByShortCodeSpec(shortCode), 
        cancellationToken);
    return existingProduct == null || existingProduct.Id == dto.ProductsCatalogId;
  }
}
