using Ardalis.SharedKernel;
using ColmAppInventariosApi.Core.Aggregates.ProductsCatalogAggregate;
using ColmAppInventariosApi.UseCases.ProductsCatalog.Dtos;
using FluentValidation;

namespace ColmAppInventariosApi.UseCases.ProductsCatalog.Validators;

public class ProductsCatalogRequestDtoValidator : AbstractValidator<ProductsCatalogRequestDto>
{
  private readonly IRepository<ProductsCatalog> _productsCatalogRepository;

  public ProductsCatalogRequestDtoValidator(IRepository<ProductsCatalog> productsCatalogRepository)
  {
    _productsCatalogRepository = productsCatalogRepository;

    RuleFor(x => x.Name)
        .NotEmpty().WithMessage("El nombre del producto es obligatorio")
        .MaximumLength(100).WithMessage("El nombre del producto no puede exceder 100 caracteres");

    RuleFor(x => x.Description)
        .NotEmpty().WithMessage("La descripción del producto es obligatoria")
        .MaximumLength(250).WithMessage("La descripción del producto no puede exceder 250 caracteres");

    RuleFor(x => x.Barcode)
        .GreaterThan(0).WithMessage("El código de barras debe ser mayor que 0")
        .MustAsync(BeUniqueBarcodeAsync).WithMessage("El código de barras ya existe");

    RuleFor(x => x.ShortCode)
        .GreaterThan(0).WithMessage("El código corto debe ser mayor que 0")
        .MustAsync(BeUniqueShortCodeAsync).WithMessage("El código corto ya existe");

    RuleFor(x => x.MeasureUnit)
        .NotEmpty().WithMessage("La unidad de medida es obligatoria")
        .MaximumLength(100).WithMessage("La unidad de medida no puede exceder 100 caracteres");
  }

  private async Task<bool> BeUniqueBarcodeAsync(int barcode, CancellationToken cancellationToken)
  {
    var existingProduct = await _productsCatalogRepository.FirstOrDefaultAsync(
        new Core.Aggregates.ProductsCatalogAggregate.Specs.GetProductsCatalogByBarcodeSpec(barcode), 
        cancellationToken);
    return existingProduct == null;
  }

  private async Task<bool> BeUniqueShortCodeAsync(int shortCode, CancellationToken cancellationToken)
  {
    var existingProduct = await _productsCatalogRepository.FirstOrDefaultAsync(
        new Core.Aggregates.ProductsCatalogAggregate.Specs.GetProductsCatalogByShortCodeSpec(shortCode), 
        cancellationToken);
    return existingProduct == null;
  }
}
