using Ardalis.Result;
using Ardalis.SharedKernel;
using AutoMapper;
using ColmAppInventariosApi.Core.Aggregates.ProductsCatalogAggregate;
using ColmAppInventariosApi.Core.Interfaces;
using ColmAppInventariosApi.UseCases.ProductsCatalog.Dtos;

namespace ColmAppInventariosApi.UseCases.ProductsCatalog.Commands.Create;

public class CreateProductsCatalogHandler : ICommandHandler<CreateProductsCatalogCommand, Result<ProductsCatalogResponseDto>>
{
  private readonly IRepository<Core.Aggregates.ProductsCatalogAggregate.ProductsCatalog> _repository;
  private readonly IMapper _mapper;
  private readonly ILogService _logService;

  public CreateProductsCatalogHandler(
      IRepository<Core.Aggregates.ProductsCatalogAggregate.ProductsCatalog> repository,
      IMapper mapper,
      ILogService logService)
  {
    _repository = repository;
    _mapper = mapper;
    _logService = logService;
  }

  public async Task<Result<ProductsCatalogResponseDto>> Handle(CreateProductsCatalogCommand request, CancellationToken cancellationToken)
  {
    await _logService.LogInfoAsync($"Creando nuevo producto en catálogo con nombre: {request.Dto.Name}");

    try
    {
      var productsCatalogEntity = _mapper.Map<Core.Aggregates.ProductsCatalogAggregate.ProductsCatalog>(request.Dto);
      await _repository.AddAsync(productsCatalogEntity, cancellationToken);
      var productsCatalogResponse = _mapper.Map<ProductsCatalogResponseDto>(productsCatalogEntity);

      await _logService.LogSuccessAsync($"Producto creado exitosamente ID: {productsCatalogEntity.Id}, Nombre: {productsCatalogEntity.Name}");
      return Result.Success(productsCatalogResponse);
    }
    catch (Exception ex)
    {
      await _logService.LogErrorAsync($"Error al crear producto: {request.Dto.Name}", ex);
      return Result.Error(ex.Message);
    }
  }
}
