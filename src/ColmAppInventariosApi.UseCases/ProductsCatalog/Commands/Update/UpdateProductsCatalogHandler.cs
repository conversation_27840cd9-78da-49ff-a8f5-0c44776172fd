using Ardalis.Result;
using Ardalis.SharedKernel;
using AutoMapper;
using ColmAppInventariosApi.Core.Aggregates.ProductsCatalogAggregate;
using ColmAppInventariosApi.Core.Interfaces;
using ColmAppInventariosApi.UseCases.ProductsCatalog.Dtos;

namespace ColmAppInventariosApi.UseCases.ProductsCatalog.Commands.Update;

public class UpdateProductsCatalogHandler : ICommandHandler<UpdateProductsCatalogCommand, Result<ProductsCatalogResponseDto>>
{
  private readonly IRepository<Core.Aggregates.ProductsCatalogAggregate.ProductsCatalog> _repository;
  private readonly IMapper _mapper;
  private readonly ILogService _logService;

  public UpdateProductsCatalogHandler(
      IRepository<Core.Aggregates.ProductsCatalogAggregate.ProductsCatalog> repository,
      IMapper mapper,
      ILogService logService)
  {
    _repository = repository;
    _mapper = mapper;
    _logService = logService;
  }

  public async Task<Result<ProductsCatalogResponseDto>> Handle(UpdateProductsCatalogCommand request, CancellationToken cancellationToken)
  {
    await _logService.LogInfoAsync($"Actualizando producto ID: {request.UpdateDto.ProductsCatalogId}");

    try
    {
      var productsCatalog = await _repository.GetByIdAsync(request.UpdateDto.ProductsCatalogId, cancellationToken);

      if (productsCatalog is null)
      {
        await _logService.LogWarningAsync($"Actualización fallida: Producto ID: {request.UpdateDto.ProductsCatalogId} no encontrado");
        return Result.NotFound("Producto no encontrado");
      }

      var oldName = productsCatalog.Name;
      _mapper.Map(request.UpdateDto, productsCatalog);
      await _repository.UpdateAsync(productsCatalog, cancellationToken);
      var productsCatalogResponse = _mapper.Map<ProductsCatalogResponseDto>(productsCatalog);

      await _logService.LogSuccessAsync($"Producto actualizado exitosamente ID: {productsCatalog.Id} de '{oldName}' a '{productsCatalog.Name}'");
      return Result.Success(productsCatalogResponse);
    }
    catch (Exception ex)
    {
      await _logService.LogErrorAsync($"Error al actualizar producto ID: {request.UpdateDto.ProductsCatalogId}", ex);
      return Result.Error(ex.Message);
    }
  }
}
