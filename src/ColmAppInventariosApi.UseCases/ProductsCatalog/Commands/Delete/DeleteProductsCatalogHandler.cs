using Ardalis.Result;
using Ardalis.SharedKernel;
using ColmAppInventariosApi.Core.Aggregates.ProductsCatalogAggregate;
using ColmAppInventariosApi.Core.Interfaces;

namespace ColmAppInventariosApi.UseCases.ProductsCatalog.Commands.Delete;

public class DeleteProductsCatalogHandler : ICommandHandler<DeleteProductsCatalogCommand, Result>
{
  private readonly IRepository<Core.Aggregates.ProductsCatalogAggregate.ProductsCatalog> _productsCatalogRepository;
  private readonly ILogService _logService;

  public DeleteProductsCatalogHandler(
      IRepository<Core.Aggregates.ProductsCatalogAggregate.ProductsCatalog> productsCatalogRepository,
      ILogService logService)
  {
    _productsCatalogRepository = productsCatalogRepository;
    _logService = logService;
  }

  public async Task<Result> Handle(DeleteProductsCatalogCommand request, CancellationToken cancellationToken)
  {
    await _logService.LogInfoAsync($"Eliminando producto ID: {request.ProductsCatalogId}");

    try
    {
      var productsCatalog = await _productsCatalogRepository.GetByIdAsync(request.ProductsCatalogId, cancellationToken);

      if (productsCatalog is null)
      {
        await _logService.LogWarningAsync($"Eliminación fallida: Producto ID: {request.ProductsCatalogId} no encontrado");
        return Result.NotFound("Producto no encontrado");
      }

      await _productsCatalogRepository.DeleteAsync(productsCatalog, cancellationToken);

      await _logService.LogSuccessAsync($"Producto eliminado exitosamente ID: {productsCatalog.Id}, Nombre: {productsCatalog.Name}");
      return Result.Success();
    }
    catch (Exception ex)
    {
      await _logService.LogErrorAsync($"Error al eliminar producto ID: {request.ProductsCatalogId}", ex);
      return Result.Error(ex.Message);
    }
  }
}
