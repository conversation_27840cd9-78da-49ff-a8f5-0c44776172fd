﻿using Ardalis.Result;
using Ardalis.SharedKernel;
using ColmAppInventariosApi.Core.Abstract;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate.Models;
using ColmAppInventariosApi.UseCases.BankTransactions.Dtos;

namespace ColmAppInventariosApi.UseCases.BankTransactions.Queries.ListPaginated;
public record ListPaginatedTransactionQuery(TransactionFilterParameters Filter)
    : IQuery<Result<PaginationResult<TransactionResponseDto>>>;
