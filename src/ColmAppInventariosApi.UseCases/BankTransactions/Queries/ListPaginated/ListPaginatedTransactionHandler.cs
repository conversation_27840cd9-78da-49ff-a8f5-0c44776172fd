﻿using Ardalis.Result;
using Ardalis.SharedKernel;
using AutoMapper;
using ColmAppInventariosApi.Core.Abstract;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate.Specs;
using ColmAppInventariosApi.Core.Interfaces;
using ColmAppInventariosApi.UseCases.BankTransactions.Dtos;
using ColmAppInventariosApi.UseCases.Commons.Paginations;

namespace ColmAppInventariosApi.UseCases.BankTransactions.Queries.ListPaginated;

public class ListPaginatedTransactionHandler : IQueryHandler<ListPaginatedTransactionQuery, Result<PaginationResult<TransactionResponseDto>>>
{
  private readonly IReadRepository<BankTransaction> _transactionReadRepository;
  private readonly IMapper _mapper;
  private readonly ILogService _logService;

  public ListPaginatedTransactionHandler(
      IReadRepository<BankTransaction> transactionReadRepository,
      IMapper mapper,
      ILogService logService)
  {
    _transactionReadRepository = transactionReadRepository;
    _mapper = mapper;
    _logService = logService;
  }

  public async Task<Result<PaginationResult<TransactionResponseDto>>> Handle(
      ListPaginatedTransactionQuery request,
      CancellationToken cancellationToken)
  {
    await _logService.LogInfoAsync($"Recuperando transacciones paginadas " +
                                  $"(Página: {request.Filter.PageNumber}, Tamaño: {request.Filter.PageSize}, " +
                                  $"BankAccountId: {request.Filter.BankAccountId})");
    try
    {
      var filterSpec = new GetTransactionPaginateSpec(request.Filter);
      var pagedResult = await _transactionReadRepository.GetPagedAsync<BankTransaction, TransactionResponseDto>(
          filterSpec,
          request.Filter.PageNumber,
          request.Filter.PageSize,
          _mapper,
          cancellationToken);

      await _logService.LogSuccessAsync($"Transacciones paginadas recuperadas exitosamente " +
                                     $"(Página: {request.Filter.PageNumber}, Tamaño: {request.Filter.PageSize}, " +
                                     $"Recuperadas: {pagedResult.Items.Count()}/{pagedResult.TotalCount}, " +
                                     $"BankAccountId: {request.Filter.BankAccountId})");

      return pagedResult.ToSuccessResult();
    }
    catch (Exception ex)
    {
      await _logService.LogErrorAsync($"Error al recuperar transacciones paginadas " +
                                   $"(Página: {request.Filter.PageNumber}, Tamaño: {request.Filter.PageSize}, " +
                                   $"BankAccountId: {request.Filter.BankAccountId})", ex);

      return Result.Error(ex.Message);
    }
  }
}
