﻿using Ardalis.SharedKernel;
using FluentValidation;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate.Models;

namespace ColmAppInventariosApi.UseCases.BankTransactions.Validators;

public class TransactionFilterParametersValidator : AbstractValidator<TransactionFilterParameters>
{
  private readonly IRepository<BankAccount> _bankAccountRepository;

  public TransactionFilterParametersValidator(IRepository<BankAccount> bankAccountRepository)
  {
    _bankAccountRepository = bankAccountRepository;

    RuleFor(x => x.BankAccountId)
        .NotEqual(Guid.Empty).WithMessage("El ID de la cuenta bancaria es obligatorio")
        .MustAsync(ExistInDatabase).WithMessage("Cuenta bancaria no encontrada");

    RuleFor(x => x.PageNumber)
        .GreaterThan(0).WithMessage("El número de página debe ser mayor que 0");

    RuleFor(x => x.PageSize)
        .GreaterThan(0).WithMessage("El tamaño de página debe ser mayor que 0")
        .LessThanOrEqualTo(100).WithMessage("El tamaño de página debe estar entre 1 y 100");

    RuleFor(x => x.MinAmount)
        .GreaterThanOrEqualTo(0).When(x => x.MinAmount.HasValue)
        .WithMessage("El monto mínimo no puede ser negativo");

    RuleFor(x => x.MaxAmount)
        .GreaterThanOrEqualTo(0).When(x => x.MaxAmount.HasValue)
        .WithMessage("El monto máximo no puede ser negativo")
        .GreaterThanOrEqualTo(x => x.MinAmount ?? 0).When(x => x.MinAmount.HasValue && x.MaxAmount.HasValue)
        .WithMessage("El monto máximo debe ser mayor que el monto mínimo");

    RuleFor(x => x.StartDate)
        .LessThanOrEqualTo(DateTime.UtcNow).When(x => x.StartDate.HasValue)
        .WithMessage("La fecha de inicio no puede estar en el futuro");

    RuleFor(x => x.EndDate)
        .LessThanOrEqualTo(DateTime.UtcNow).When(x => x.EndDate.HasValue)
        .WithMessage("La fecha final no puede estar en el futuro")
        .GreaterThanOrEqualTo(x => x.StartDate ?? DateTime.MinValue).When(x => x.StartDate.HasValue && x.EndDate.HasValue)
        .WithMessage("La fecha final debe ser mayor que la fecha de inicio");

    RuleFor(x => x.TransactionType)
        .IsInEnum().When(x => x.TransactionType.HasValue)
        .WithMessage("Tipo de transacción inválido");
  }

  private async Task<bool> ExistInDatabase(Guid bankAccountId, CancellationToken cancellationToken)
  {
    var bankAccount = await _bankAccountRepository.GetByIdAsync(bankAccountId, cancellationToken);
    return bankAccount != null;
  }
}
