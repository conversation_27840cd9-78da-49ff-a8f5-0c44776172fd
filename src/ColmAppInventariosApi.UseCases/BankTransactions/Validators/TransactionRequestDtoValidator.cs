﻿using Ardalis.SharedKernel;
using FluentValidation;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate;
using ColmAppInventariosApi.UseCases.BankTransactions.Dtos;

namespace ColmAppInventariosApi.UseCases.BankTransactions.Validators;

public class TransactionRequestDtoValidator : AbstractValidator<TransactionRequestDto>
{
  private readonly IRepository<BankAccount> _bankAccountRepository;

  public TransactionRequestDtoValidator(IRepository<BankAccount> bankAccountRepository)
  {
    _bankAccountRepository = bankAccountRepository;

    RuleFor(x => x.BankAccountId)
        .NotEqual(Guid.Empty).WithMessage("El ID de la cuenta bancaria es obligatorio")
        .MustAsync(ExistInDatabase).WithMessage("Cuenta bancaria no encontrada");

    RuleFor(x => x.Amount)
        .GreaterThan(0).WithMessage("El monto debe ser mayor que 0");

    RuleFor(x => x.Description)
        .NotEmpty().WithMessage("La descripción es obligatoria");

    RuleFor(x => x.Type)
        .IsInEnum().WithMessage("Tipo de transacción inválido");
  }

  private async Task<bool> ExistInDatabase(Guid bankAccountId, CancellationToken cancellationToken)
  {
    var bankAccount = await _bankAccountRepository.GetByIdAsync(bankAccountId, cancellationToken);
    return bankAccount != null;
  }
}
