﻿using Ardalis.Result;
using Ardalis.SharedKernel;
using AutoMapper;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate.Events;
using ColmAppInventariosApi.Core.Interfaces;
using ColmAppInventariosApi.UseCases.BankTransactions.Dtos;
using MediatR;

namespace ColmAppInventariosApi.UseCases.BankTransactions.Commands.Create;

public class CreateTransactionHandler : ICommandHandler<CreateTransactionCommand, Result<TransactionResponseDto>>
{
  private readonly IMediator _mediator;
  private readonly IRepository<BankAccount> _bankAccountRepository;
  private readonly IMapper _mapper;
  private readonly ILogService _logService;

  public CreateTransactionHandler(
      IMediator mediator,
      IRepository<BankAccount> bankAccountRepository,
      IMapper mapper,
      ILogService logService)
  {
    _mediator = mediator;
    _bankAccountRepository = bankAccountRepository;
    _mapper = mapper;
    _logService = logService;
  }

  public async Task<Result<TransactionResponseDto>> Handle(CreateTransactionCommand request, CancellationToken cancellationToken)
  {
    await _logService.LogInfoAsync($"Creando transacción para cuenta bancaria ID: {request.Dto.BankAccountId}, Monto: {request.Dto.Amount}");

    try
    {
      var bankAccount = await _bankAccountRepository.GetByIdAsync(request.Dto.BankAccountId, cancellationToken);
      if (bankAccount == null)
      {
        await _logService.LogWarningAsync($"Creación de transacción fallida: Cuenta bancaria ID: {request.Dto.BankAccountId} no encontrada");
        return Result.Error("Cuenta bancaria no encontrada");
      }

      var transaction = _mapper.Map<BankTransaction>(request.Dto);
      bankAccount.Transactions.Add(transaction);
      await _bankAccountRepository.UpdateAsync(bankAccount, cancellationToken);

      var domainEvent = new BankTransactionCreatedEvent(transaction);
      await _mediator.Publish(domainEvent, cancellationToken);

      await _logService.LogSuccessAsync($"Transacción creada exitosamente ID: {transaction.Id} " +
                                     $"para cuenta bancaria: {bankAccount.Id}, " +
                                     $"Monto: {transaction.Amount}, " +
                                     $"Tipo: {transaction.TransactionType}");

      return Result.Success(_mapper.Map<TransactionResponseDto>(transaction));
    }
    catch (Exception ex)
    {
      await _logService.LogErrorAsync($"Error al crear transacción para cuenta bancaria ID: {request.Dto.BankAccountId}", ex);
      return Result.Error(ex.Message);
    }
  }
}
