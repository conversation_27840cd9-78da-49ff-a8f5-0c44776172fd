﻿using ColmAppInventariosApi.Core.Abstract;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate.Enums;

namespace ColmAppInventariosApi.UseCases.BankTransactions.Dtos;
public class TransactionResponseDto : BaseResponseDto
{
  public decimal Amount { get; set; }
  public string Description { get; set; } = string.Empty;
  public TransactionType Type { get; set; }
  public DateTime TransactionDate { get; set; }
}
