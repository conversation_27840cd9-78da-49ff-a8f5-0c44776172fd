﻿using Ardalis.Result;
using Ardalis.SharedKernel;
using Ardalis.Specification;
using AutoMapper;
using ColmAppInventariosApi.Core.Abstract;
using ColmAppInventariosApi.Core.Aggregates.AbstractAggregate.Specs;

namespace ColmAppInventariosApi.UseCases.Commons.Paginations;

/// <summary>
/// Pagination extensions for repositories
/// </summary>
public static class PaginationExtensions
{
  /// <summary>
  /// Gets a paginated list of entities using a specification
  /// </summary>
  public static async Task<PaginationResult<T>> GetPagedAsync<T>(
      this IReadRepository<T> repository,
      ISpecification<T> specification,
      int pageNumber,
      int pageSize,
      CancellationToken cancellationToken = default) where T : BaseEntity, IAggregateRoot
  {
    // Apply pagination to the specification
    var paginatedSpec = new FilterPaginationSpecification<T>(specification, pageNumber, pageSize);

    // Get items using the paginated specification
    var items = await repository.ListAsync(paginatedSpec, cancellationToken);

    // Get total count using the original specification (without pagination)
    var totalCount = await repository.CountAsync(specification, cancellationToken);

    // Return a new paged result
    return new PaginationResult<T>(items, totalCount, pageNumber, pageSize);
  }

  /// <summary>
  /// Gets a paginated list of entities without any additional filtering
  /// </summary>
  public static async Task<PaginationResult<T>> GetPagedAsync<T>(
      this IReadRepository<T> repository,
      int pageNumber,
      int pageSize,
      CancellationToken cancellationToken = default) where T : BaseEntity, IAggregateRoot
  {
    // Create a simple pagination specification
    var paginationSpec = new PaginationSpecification<T>(pageNumber, pageSize);

    // Get items using the pagination specification
    var items = await repository.ListAsync(paginationSpec, cancellationToken);

    // Get total count of all items
    var totalCount = await repository.CountAsync(cancellationToken);

    // Return a new paged result
    return new PaginationResult<T>(items, totalCount, pageNumber, pageSize);
  }

  /// <summary>
  /// Gets a paginated list of entities mapped to DTOs using a specification
  /// </summary>
  public static async Task<PaginationResult<TDto>> GetPagedAsync<TEntity, TDto>(
      this IReadRepository<TEntity> repository,
      ISpecification<TEntity> specification,
      int pageNumber,
      int pageSize,
      IMapper mapper,
      CancellationToken cancellationToken = default) where TEntity : BaseEntity, IAggregateRoot
  {
    // Apply pagination to the specification
    var paginatedSpec = new FilterPaginationSpecification<TEntity>(specification, pageNumber, pageSize);

    // Get items using the paginated specification
    var items = await repository.ListAsync(paginatedSpec, cancellationToken);

    // Map the entities to DTOs
    var mappedItems = mapper.Map<List<TDto>>(items);

    // Get total count using the original specification (without pagination)
    var totalCount = await repository.CountAsync(specification, cancellationToken);

    // Return a new paged result
    return new PaginationResult<TDto>(mappedItems, totalCount, pageNumber, pageSize);
  }

  /// <summary>
  /// Gets a paginated list of entities mapped to DTOs without any additional filtering
  /// </summary>
  public static async Task<PaginationResult<TDto>> GetPagedAsync<TEntity, TDto>(
      this IReadRepository<TEntity> repository,
      int pageNumber,
      int pageSize,
      IMapper mapper,
      CancellationToken cancellationToken = default) where TEntity : BaseEntity, IAggregateRoot
  {
    // Create a simple pagination specification
    var paginationSpec = new PaginationSpecification<TEntity>(pageNumber, pageSize);

    // Get items using the pagination specification
    var items = await repository.ListAsync(paginationSpec, cancellationToken);

    // Map the entities to DTOs
    var mappedItems = mapper.Map<List<TDto>>(items);

    // Get total count of all items
    var totalCount = await repository.CountAsync(cancellationToken);

    // Return a new paged result
    return new PaginationResult<TDto>(mappedItems, totalCount, pageNumber, pageSize);
  }

  /// <summary>
  /// Simplifies creation of a successful paginated result
  /// </summary>
  public static Result<PaginationResult<T>> ToSuccessResult<T>(this PaginationResult<T> pagedResult)
  {
    return Result.Success(pagedResult);
  }
}
