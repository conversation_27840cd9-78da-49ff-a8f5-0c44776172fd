﻿using Ardalis.Result;
using FluentValidation;
using MediatR;

namespace ColmAppInventariosApi.UseCases.Commons.Behaviors;
public class ValidationBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>
    where TResponse : class
{
  private readonly IEnumerable<IValidator<TRequest>> _validators;

  public ValidationBehavior(IEnumerable<IValidator<TRequest>> validators)
  {
    _validators = validators;
  }

  public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
  {
    if (!_validators.Any())
      return await next();

    var context = new ValidationContext<TRequest>(request);
    var validationResults = await Task.WhenAll(
        _validators.Select(v => v.ValidateAsync(context, cancellationToken)));

    var failures = validationResults
        .SelectMany(r => r.Errors)
        .Where(f => f != null)
        .ToList();

    if (failures.Count == 0)
      return await next();

    // If the response type is Ardalis.Result<T>, we can create a validation error result
    if (typeof(TResponse).IsGenericType && typeof(TResponse).GetGenericTypeDefinition() == typeof(Result<>))
    {
      var resultType = typeof(TResponse).GetGenericArguments()[0];

      // Convert FluentValidation failures to Ardalis ValidationErrors
      var validationErrors = failures.Select(failure => new ValidationError(
          // Use PropertyName as the identifier
          identifier: failure.PropertyName,
          // Use ErrorMessage as is
          errorMessage: failure.ErrorMessage,
          // Use ErrorCode if available, otherwise use a default
          errorCode: string.IsNullOrEmpty(failure.ErrorCode) ? "ValidationError" : failure.ErrorCode,
          // Use a default severity (Error = 0 in ValidationSeverity enum)
          severity: ValidationSeverity.Error
      )).ToList();

      // Find and use the appropriate Invalid method from Ardalis.Result
      var invalidMethod = typeof(Result)
          .GetMethods()
          .Where(m => m.Name == nameof(Result.Invalid))
          .Where(m => m.IsGenericMethod)
          .FirstOrDefault(m =>
          {
            var parameters = m.GetParameters();
            return parameters.Length == 1 &&
                         parameters[0].ParameterType == typeof(List<ValidationError>);
          });

      if (invalidMethod != null)
      {
        var genericMethod = invalidMethod.MakeGenericMethod(resultType);
        var result = genericMethod.Invoke(null, new object[] { validationErrors });
        return result as TResponse;
      }

      // Fallback to simple string errors if we can't find the proper method
      var errorMessages = failures.Select(x => x.ErrorMessage).ToList();
      var stringErrorMethod = typeof(Result)
          .GetMethod(nameof(Result.Invalid), new[] { typeof(List<string>) })
          ?.MakeGenericMethod(resultType);

      if (stringErrorMethod != null)
      {
        var result = stringErrorMethod.Invoke(null, new object[] { errorMessages });
        return result as TResponse;
      }
    }

    // If we can't create a Result<T>, throw an exception
    throw new ValidationException(failures);
  }
}
