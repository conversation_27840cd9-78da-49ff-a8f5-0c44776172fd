﻿namespace ColmAppInventariosApi.Core.Abstract;

/// <summary>
/// Generic paged result containing items and pagination metadata
/// </summary>
public class PaginationResult<T>
{
  public IReadOnlyList<T> Items { get; }
  public int PageNumber { get; }
  public int PageSize { get; }
  public int TotalPages { get; }
  public int TotalCount { get; }
  public bool HasPreviousPage => PageNumber > 1;
  public bool HasNextPage => PageNumber < TotalPages;

  public PaginationResult(IEnumerable<T> items, int totalCount, int pageNumber, int pageSize)
  {
    var itemsList = items as IReadOnlyList<T> ?? new List<T>(items).AsReadOnly();

    Items = itemsList;
    TotalCount = totalCount;
    PageNumber = pageNumber;
    PageSize = pageSize;
    TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
  }
}
