using Ardalis.SharedKernel;
using ColmAppInventariosApi.Core.Abstract;

namespace ColmAppInventariosApi.Core.Aggregates.ProductsCatalogAggregate;

public class ProductsCatalog : BaseEntity, IAggregateRoot
{
  public string Name { get; set; } = string.Empty;
  public string Description { get; set; } = string.Empty;
  public int Barcode { get; set; }
  public int ShortCode { get; set; }
  public string MeasureUnit { get; set; } = string.Empty;
}
