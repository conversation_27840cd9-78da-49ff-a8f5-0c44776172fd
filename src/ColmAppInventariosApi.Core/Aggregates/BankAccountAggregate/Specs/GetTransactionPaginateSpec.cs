﻿using Ardalis.Specification;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate.Models;

namespace ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate.Specs;
public class GetTransactionPaginateSpec : Specification<BankTransaction>
{
  public GetTransactionPaginateSpec(TransactionFilterParameters filter)
  {
    Query
        .Where(t => t.BankAccountId == filter.BankAccountId)
        .Where(t => !filter.TransactionType.HasValue || t.TransactionType == filter.TransactionType)
        .Where(t => !filter.MinAmount.HasValue || t.Amount >= filter.MinAmount)
        .Where(t => !filter.MaxAmount.HasValue || t.Amount <= filter.MaxAmount)
        .Where(t => !filter.StartDate.HasValue || t.CreatedDate >= filter.StartDate)
        .Where(t => !filter.EndDate.HasValue || t.CreatedDate <= filter.EndDate);
  }
}
