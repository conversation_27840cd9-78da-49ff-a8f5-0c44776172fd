﻿using Ardalis.SharedKernel;
using ColmAppInventariosApi.Core.Abstract;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate.Enums;

namespace ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate;
public class BankTransaction : BaseEntity, IAggregateRoot
{
  public Guid BankAccountId { get; set; }
  public decimal Amount { get; set; }
  public string Description { get; set; } = string.Empty;
  public TransactionType TransactionType { get; set; }
}
