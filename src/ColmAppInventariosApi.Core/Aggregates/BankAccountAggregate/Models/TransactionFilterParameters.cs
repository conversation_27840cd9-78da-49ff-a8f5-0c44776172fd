﻿using ColmAppInventariosApi.Core.Abstract;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate.Enums;

namespace ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate.Models;
public class TransactionFilterParameters : PaginationFilter
{
  public Guid BankAccountId { get; set; }
  public TransactionType? TransactionType { get; set; }
  public decimal? MinAmount { get; set; }
  public decimal? MaxAmount { get; set; }
  public DateTime? StartDate { get; set; }
  public DateTime? EndDate { get; set; }
}
