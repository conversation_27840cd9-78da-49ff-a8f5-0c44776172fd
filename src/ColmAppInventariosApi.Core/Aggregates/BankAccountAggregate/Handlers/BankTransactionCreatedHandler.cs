﻿using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate.Events;
using MediatR;

namespace ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate.Handlers;

public class BankTransactionCreatedHandler : INotificationHandler<BankTransactionCreatedEvent>
{
  public async Task Handle(BankTransactionCreatedEvent domainEvent, CancellationToken cancellationToken)
  {
    // Do something with the domain event, For example: Send an email to the user that the transaction has been created.
  }
}
