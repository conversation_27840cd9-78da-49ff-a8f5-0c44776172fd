﻿using Ardalis.SharedKernel;
using ColmAppInventariosApi.Core.Abstract;
using ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate.Enums;

namespace ColmAppInventariosApi.Core.Aggregates.BankAccountAggregate;
public class BankAccount : BaseEntity, IAggregateRoot
{
  public string AccountNumber { get; set; } = string.Empty;
  public string AccountHolderName { get; set; } = string.Empty;
  public decimal Balance { get; set; }
  public AccountType AccountType { get; set; }
  public ICollection<BankTransaction> Transactions { get; set; } = [];
}

