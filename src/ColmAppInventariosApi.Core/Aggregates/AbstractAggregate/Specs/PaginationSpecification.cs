﻿using Ardalis.SharedKernel;
using Ardalis.Specification;
using ColmAppInventariosApi.Core.Abstract;

namespace ColmAppInventariosApi.Core.Aggregates.AbstractAggregate.Specs;
public class PaginationSpecification<T> : Specification<T> where T : BaseEntity, IAggregateRoot
{
  public PaginationSpecification(int pageNumber, int pageSize)
  {
    Query
        .Skip((pageNumber - 1) * pageSize)
        .Take(pageSize);
  }
}
