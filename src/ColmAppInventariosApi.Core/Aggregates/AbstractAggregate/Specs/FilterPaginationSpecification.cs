﻿using Ardalis.SharedKernel;
using Ardalis.Specification;
using ColmAppInventariosApi.Core.Abstract;

namespace ColmAppInventariosApi.Core.Aggregates.AbstractAggregate.Specs;

/// <summary>
/// A specification that applies pagination constraints to filter results
/// This class combines another specification with pagination logic
/// </summary>
public class FilterPaginationSpecification<T> : Specification<T>, ISpecification<T> where T : BaseEntity, IAggregateRoot
{
  public FilterPaginationSpecification(ISpecification<T> filterSpecification, int pageNumber, int pageSize)
  {
    // Apply where conditions
    foreach (var whereExpression in filterSpecification.WhereExpressions)
    {
      Query.Where(whereExpression.Filter);
    }

    // Apply ordering - we need to handle only the first order expression directly
    // as the Query builder works differently after the first ordering
    if (filterSpecification.OrderExpressions.Any())
    {
      var firstOrder = filterSpecification.OrderExpressions.First();

      if (firstOrder.OrderType == OrderTypeEnum.OrderBy)
      {
        Query.OrderBy(firstOrder.KeySelector);
      }
      else if (firstOrder.OrderType == OrderTypeEnum.OrderByDescending)
      {
        Query.OrderByDescending(firstOrder.KeySelector);
      }
    }

    // Apply includes - add all string includes
    foreach (var includeString in filterSpecification.IncludeStrings)
    {
      Query.Include(includeString);
    }

    // Apply pagination
    Query.Skip((pageNumber - 1) * pageSize).Take(pageSize);
  }
}

