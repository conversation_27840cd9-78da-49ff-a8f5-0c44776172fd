﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <Sdk Name="Microsoft.Build.CentralPackageVersions" Version="2.1.3" />

  <ItemGroup>
    <PackageReference Include="Ardalis.GuardClauses" />
    <PackageReference Include="Ardalis.Result" />
    <PackageReference Include="Ardalis.SharedKernel" />
    <PackageReference Include="Ardalis.SmartEnum" />
    <PackageReference Include="Ardalis.Specification" />
    <PackageReference Include="Autofac" />
    <PackageReference Include="MediatR" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Negotiate" />
    <PackageReference Include="System.ServiceModel.Duplex" />
    <PackageReference Include="System.ServiceModel.Federation" />
    <PackageReference Include="System.ServiceModel.Http" />
    <PackageReference Include="System.ServiceModel.NetTcp" />
    <PackageReference Include="System.ServiceModel.Security" />
    <PackageReference Include="Microsoft.Extensions.Configuration" />
    <PackageReference Include="Newtonsoft.Json" />
  </ItemGroup>

</Project>
