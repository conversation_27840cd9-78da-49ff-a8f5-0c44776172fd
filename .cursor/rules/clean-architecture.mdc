---
description: 
globs: 
alwaysApply: false
---
# Clean Architecture Implementation Guidelines

## Project Structure
- Core: Domain entities, interfaces, and business rules
- UseCases: Application business rules and use cases
- Infrastructure: External interfaces implementations
- Web: API endpoints and web-specific code

## Domain Layer (Core)
- Define entities as POCOs with business rules
- Use Ardalis.SharedKernel for base classes
- Implement IAggregateRoot for aggregate roots
- Use domain events for cross-cutting concerns
- Keep domain logic pure and framework-independent

Example:
```csharp
public class BankAccount : BaseEntity, IAggregateRoot
{
    public string AccountNumber { get; set; } = string.Empty;
    public string AccountHolderName { get; set; } = string.Empty;
    public decimal Balance { get; set; }
    public AccountType AccountType { get; set; }
    public ICollection<BankTransaction> Transactions { get; set; } = [];
}
```

## Application Layer (UseCases)
- Use CQRS pattern with MediatR
- Implement validators using FluentValidation
- Use Ardalis.Result for operation outcomes
- Keep use cases focused and single-purpose
- Use DTOs for data transfer

Example:
```csharp
public class CreateBankAccountCommand : ICommand<Result<BankAccountResponseDto>>
{
    public string AccountNumber { get; set; } = string.Empty;
    public string AccountHolderName { get; set; } = string.Empty;
    public decimal Balance { get; set; }
    public AccountType AccountType { get; set; }
}
```

## Infrastructure Layer
- Use Entity Framework Core for data access
- Implement repository interfaces
- Use specifications for complex queries
- Implement soft delete pattern
- Use audit entities

Example:
```csharp
public class AppDbContext : DbContext
{
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        foreach (var type in modelBuilder.Model.GetEntityTypes())
        {
            if (typeof(BaseEntity).IsAssignableFrom(type.ClrType))
                modelBuilder.SetSoftDeleteFilter(type.ClrType);
        }
    }
}
```

## Presentation Layer (Web)
- Use FastEndpoints for API endpoints
- Implement proper validation
- Use proper HTTP status codes
- Implement proper error handling
- Keep endpoints thin

Example:
```csharp
public class CreateBankAccountEndpoint : Endpoint<CreateBankAccountRequest>
{
    private readonly IMediator _mediator;

    public override void Configure()
    {
        Post("/api/bankaccounts");
        AllowAnonymous();
    }

    public override async Task HandleAsync(CreateBankAccountRequest req, CancellationToken ct)
    {
        var result = await _mediator.Send(new CreateBankAccountCommand(req), ct);
        await SendResultAsync(result);
    }
}
```

## Dependency Injection
- Use Autofac for DI
- Register services in appropriate modules
- Use constructor injection
- Follow SOLID principles
- Use interfaces for dependencies

Example:
```csharp
public class AutofacInfrastructureModule : Module
{
    protected override void Load(ContainerBuilder builder)
    {
        builder.RegisterType<AppDbContext>()
            .AsSelf()
            .InstancePerLifetimeScope();
    }
}
```

## Validation
- Use FluentValidation for input validation
- Implement custom validators
- Use async validation when needed
- Validate business rules
- Return meaningful error messages

Example:
```csharp
public class BankAccountRequestDtoValidator : AbstractValidator<BankAccountRequestDto>
{
    public BankAccountRequestDtoValidator(IRepository<BankAccount> bankAccountRepository)
    {
        RuleFor(x => x.AccountNumber)
            .NotEmpty()
            .MustAsync(BeUniqueAccountNumber)
            .WithMessage("Account number must be unique");
    }
}
```

## Error Handling
- Use global exception handler
- Implement proper error responses
- Use proper HTTP status codes
- Include meaningful error messages
- Log errors appropriately

Example:
```csharp
public class GlobalExceptionHandler : IExceptionHandler
{
    public async ValueTask<bool> TryHandleAsync(
        HttpContext context,
        Exception exception,
        CancellationToken cancellationToken)
    {
        var response = new ErrorResponse
        {
            Message = exception.Message,
            StatusCode = StatusCodes.Status500InternalServerError
        };

        await context.Response.WriteAsJsonAsync(response, cancellationToken);
        return true;
    }
}
```

## Logging
- Use Serilog for logging
- Log important operations
- Include correlation IDs
- Use structured logging
- Configure appropriate sinks

Example:
```csharp
builder.Host.UseSerilog((context, services, config) =>
    config.ReadFrom.Configuration(context.Configuration));
```

## Security
- Use proper authentication
- Implement proper authorization
- Use HTTPS
- Implement CORS properly
- Follow security best practices

Example:
```csharp
builder.Services.AddAuthentication(NegotiateDefaults.AuthenticationScheme)
    .AddNegotiate();

builder.Services.AddCors(opt =>
{
    opt.AddPolicy("CorsPolicy", builder =>
        builder.AllowAnyHeader()
               .AllowAnyMethod()
               .WithOrigins(configuration.GetSection("CorsOrigins").Get<string[]>()!)
               .AllowCredentials());
});
```

