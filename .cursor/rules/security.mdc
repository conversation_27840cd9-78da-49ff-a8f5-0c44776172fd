---
description: 
globs: 
alwaysApply: false
---
# Security Guidelines

## Authentication
- Use proper authentication
- Implement proper authorization
- Use secure authentication methods
- Use proper authentication middleware
- Use proper authentication schemes

Example:
```csharp
builder.Services.AddAuthentication(NegotiateDefaults.AuthenticationScheme)
    .AddNegotiate();

builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("RequireAdminRole", policy =>
        policy.RequireRole("Admin"));
});
```

## Authorization
- Use proper authorization
- Implement proper authorization policies
- Use proper authorization requirements
- Use proper authorization handlers
- Use proper authorization middleware

Example:
```csharp
public class BankAccountAuthorizationHandler : AuthorizationHandler<OperationAuthorizationRequirement, BankAccount>
{
    protected override Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        OperationAuthorizationRequirement requirement,
        BankAccount resource)
    {
        if (requirement.Name == Operations.Read.Name)
        {
            if (context.User.IsInRole("Admin") ||
                context.User.IsInRole("BankManager") ||
                resource.AccountHolderName == context.User.Identity?.Name)
            {
                context.Succeed(requirement);
            }
        }

        return Task.CompletedTask;
    }
}
```

## CORS
- Use proper CORS configuration
- Configure allowed origins
- Configure allowed methods
- Configure allowed headers
- Configure allowed credentials

Example:
```csharp
builder.Services.AddCors(opt =>
{
    opt.AddPolicy("CorsPolicy", builder =>
        builder.AllowAnyHeader()
               .AllowAnyMethod()
               .WithOrigins(configuration.GetSection("CorsOrigins").Get<string[]>()!)
               .AllowCredentials());
});
```

## HTTPS
- Use HTTPS in production
- Configure HTTPS properly
- Use proper SSL/TLS settings
- Use proper certificate settings
- Use proper security headers

Example:
```csharp
builder.Services.AddHttpsRedirection(options =>
{
    options.HttpsPort = 443;
});

builder.Services.AddHsts(options =>
{
    options.MaxAge = TimeSpan.FromDays(365);
    options.IncludeSubDomains = true;
    options.Preload = true;
});
```

## Security Headers
- Use proper security headers
- Configure security headers properly
- Use proper security policies
- Use proper security settings
- Use proper security middleware

Example:
```csharp
app.Use(async (context, next) =>
{
    context.Response.Headers.Add("X-Content-Type-Options", "nosniff");
    context.Response.Headers.Add("X-Frame-Options", "DENY");
    context.Response.Headers.Add("X-XSS-Protection", "1; mode=block");
    context.Response.Headers.Add("Referrer-Policy", "strict-origin-when-cross-origin");
    context.Response.Headers.Add("Content-Security-Policy", "default-src 'self'");
    await next();
});
```

## Data Protection
- Use proper data protection
- Encrypt sensitive data
- Use proper encryption methods
- Use proper key management
- Use proper security settings

Example:
```csharp
builder.Services.AddDataProtection()
    .SetApplicationName("ColmAppInventariosApi")
    .PersistKeysToFileSystem(new DirectoryInfo(@"C:\keys"));
```

## Input Validation
- Validate input properly
- Sanitize input properly
- Use proper validation methods
- Use proper sanitization methods
- Use proper security settings

Example:
```csharp
public class BankAccountRequestDtoValidator : AbstractValidator<BankAccountRequestDto>
{
    public BankAccountRequestDtoValidator()
    {
        RuleFor(x => x.AccountNumber)
            .NotEmpty()
            .Matches(@"^\d{10}$").WithMessage("Account number must be 10 digits");

        RuleFor(x => x.AccountHolderName)
            .NotEmpty()
            .MaximumLength(100)
            .Matches(@"^[a-zA-Z\s]*$").WithMessage("Account holder name must contain only letters and spaces");
    }
}
```

## Output Encoding
- Encode output properly
- Use proper encoding methods
- Use proper security settings
- Use proper security middleware
- Use proper security policies

Example:
```csharp
app.Use(async (context, next) =>
{
    context.Response.Headers.Add("Content-Type", "application/json; charset=utf-8");
    await next();
});
```

## Security Best Practices
- Use proper authentication
- Use proper authorization
- Use proper CORS configuration
- Use HTTPS in production
- Use proper security headers
- Use proper data protection
- Validate input properly
- Encode output properly
- Use proper security settings
- Use proper security middleware
- Use proper security policies
- Use proper security patterns
- Use proper security tools
- Use proper security libraries
- Use proper security frameworks
- Use proper security standards
- Use proper security guidelines
- Use proper security recommendations
- Use proper security best practices
- Use proper security patterns

