---
description: 
globs: 
alwaysApply: false
---
# Base Prompt for Project Analysis

## Initial Analysis
1. Analyze .cursor/rules directory:
   - List all rule files
   - Read each rule file content
   - Understand rule purposes
   - Identify rule relationships
   - Map rules to project structure

2. Analyze rule file structure:
   - Check rule metadata (description, globs, alwaysApply)
   - Review rule categories
   - Identify rule patterns
   - Check rule examples
   - Validate rule completeness

## Project Structure Analysis
1. Analyze the solution structure:
   - Core project (Domain layer)
   - UseCases project (Application layer)
   - Infrastructure project (Infrastructure layer)
   - Web project (Presentation layer)

2. Identify key components:
   - Domain entities and aggregates
   - Use cases and commands
   - Infrastructure implementations
   - API endpoints
   - Cross-cutting concerns

3. Analyze dependencies:
   - Project references
   - NuGet packages
   - External services
   - Framework dependencies

4. Review architecture patterns:
   - Clean Architecture principles
   - CQRS implementation
   - Repository pattern
   - Specification pattern
   - Mediator pattern

## .cursor Rules Analysis
1. Review existing rules:
   - Clean Architecture patterns (clean-architecture.mdc)
   - Testing guidelines (testing.mdc)
   - Validation patterns (validation.mdc)
   - Error handling (error-handling.mdc)
   - Security practices (security.mdc)
   - Logging patterns (logging.mdc)

2. Validate rule applicability:
   - Match rules to project structure
   - Identify missing rules
   - Remove irrelevant rules
   - Update outdated rules
   - Add new rules as needed

3. Ensure rule consistency:
   - Cross-reference rules
   - Check for contradictions
   - Verify rule completeness
   - Validate rule examples
   - Update rule documentation

4. Rule File Analysis:
   - clean-architecture.mdc:
     * Review Clean Architecture patterns
     * Check layer separation
     * Validate dependency direction
     * Review example implementations
     * Verify pattern adherence

   - testing.mdc:
     * Review testing strategies
     * Check test organization
     * Validate test patterns
     * Review test examples
     * Verify testing coverage

   - validation.mdc:
     * Review validation patterns
     * Check validation rules
     * Validate error handling
     * Review validation examples
     * Verify validation coverage

   - error-handling.mdc:
     * Review error handling patterns
     * Check error responses
     * Validate error logging
     * Review error examples
     * Verify error handling coverage

   - security.mdc:
     * Review security patterns
     * Check authentication/authorization
     * Validate security measures
     * Review security examples
     * Verify security coverage

   - logging.mdc:
     * Review logging patterns
     * Check log configuration
     * Validate log levels
     * Review logging examples
     * Verify logging coverage

## Project-Specific Guidelines
1. Follow Clean Architecture principles:
   - Keep domain logic pure
   - Use proper layering
   - Maintain dependency direction
   - Use proper abstractions
   - Follow SOLID principles

2. Implement CQRS pattern:
   - Separate commands and queries
   - Use MediatR for handling
   - Implement proper validation
   - Use proper error handling
   - Follow proper patterns

3. Use proper testing:
   - Write unit tests
   - Write integration tests
   - Use proper test patterns
   - Follow testing guidelines
   - Use proper test data

4. Implement proper validation:
   - Use FluentValidation
   - Implement proper rules
   - Handle validation errors
   - Use proper patterns
   - Follow guidelines

5. Handle errors properly:
   - Use global exception handler
   - Implement proper responses
   - Use proper status codes
   - Log errors properly
   - Follow guidelines

6. Implement security:
   - Use proper authentication
   - Implement proper authorization
   - Configure CORS properly
   - Use HTTPS
   - Follow security guidelines

7. Use proper logging:
   - Configure Serilog
   - Use proper log levels
   - Include proper context
   - Use structured logging
   - Follow logging guidelines

## Code Generation Guidelines
1. Follow project structure:
   - Place code in correct layer
   - Use proper namespaces
   - Follow naming conventions
   - Use proper patterns
   - Follow guidelines

2. Implement features:
   - Create domain entities
   - Implement use cases
   - Add infrastructure code
   - Create API endpoints
   - Follow patterns

3. Add tests:
   - Write unit tests
   - Write integration tests
   - Use proper patterns
   - Follow guidelines
   - Use proper data

4. Add validation:
   - Create validators
   - Implement rules
   - Handle errors
   - Follow patterns
   - Follow guidelines

5. Handle errors:
   - Add error handling
   - Use proper responses
   - Log errors
   - Follow patterns
   - Follow guidelines

6. Implement security:
   - Add authentication
   - Add authorization
   - Configure security
   - Follow patterns
   - Follow guidelines

7. Add logging:
   - Configure logging
   - Add log statements
   - Use proper levels
   - Follow patterns
   - Follow guidelines

## Documentation Guidelines
1. Document code:
   - Add XML comments
   - Document public APIs
   - Document complex logic
   - Follow patterns
   - Follow guidelines

2. Update rules:
   - Document new patterns
   - Update examples
   - Add new rules
   - Follow patterns
   - Follow guidelines

3. Maintain documentation:
   - Keep docs up to date
   - Review regularly
   - Update as needed
   - Follow patterns
   - Follow guidelines


