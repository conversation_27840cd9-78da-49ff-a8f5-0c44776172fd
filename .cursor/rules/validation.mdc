---
description: 
globs: 
alwaysApply: false
---
# Validation Guidelines

## FluentValidation Usage
- Use FluentValidation for input validation
- Create separate validator classes
- Use async validation when needed
- Validate business rules
- Return meaningful error messages

Example:
```csharp
public class BankAccountRequestDtoValidator : AbstractValidator<BankAccountRequestDto>
{
    private readonly IRepository<BankAccount> _bankAccountRepository;

    public BankAccountRequestDtoValidator(IRepository<BankAccount> bankAccountRepository)
    {
        _bankAccountRepository = bankAccountRepository;

        RuleFor(x => x.AccountNumber)
            .NotEmpty().WithMessage("El número de cuenta es obligatorio")
            .MustAsync(BeUniqueAccountNumber).WithMessage("El número de cuenta ya existe");

        RuleFor(x => x.AccountHolderName)
            .NotEmpty().WithMessage("El nombre del titular de la cuenta es obligatorio");

        RuleFor(x => x.Balance)
            .GreaterThanOrEqualTo(0).WithMessage("El saldo inicial no puede ser negativo");

        RuleFor(x => x.AccountType)
            .IsInEnum().WithMessage("Tipo de cuenta inválido");
    }

    private async Task<bool> BeUniqueAccountNumber(string accountNumber, CancellationToken cancellationToken)
    {
        var spec = new GetBankAccountByNumberSpec(accountNumber);
        var existingAccount = await _bankAccountRepository.FirstOrDefaultAsync(spec, cancellationToken);
        return existingAccount == null;
    }
}
```

## Validation Registration
- Register validators in DI container
- Use assembly scanning
- Register validators for DTOs
- Register validators for commands
- Register validators for queries

Example:
```csharp
builder.Services.AddValidatorsFromAssemblyContaining<BankAccountRequestDtoValidator>();
```

## Validation Behavior
- Use MediatR pipeline behavior
- Validate requests before handling
- Return validation errors
- Use proper HTTP status codes
- Include validation error details

Example:
```csharp
public class ValidationBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>
{
    private readonly IEnumerable<IValidator<TRequest>> _validators;

    public ValidationBehavior(IEnumerable<IValidator<TRequest>> validators)
    {
        _validators = validators;
    }

    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        if (_validators.Any())
        {
            var context = new ValidationContext<TRequest>(request);
            var validationResults = await Task.WhenAll(
                _validators.Select(v => v.ValidateAsync(context, cancellationToken)));

            var failures = validationResults
                .SelectMany(r => r.Errors)
                .Where(f => f != null)
                .ToList();

            if (failures.Count != 0)
                throw new ValidationException(failures);
        }
        return await next();
    }
}
```

## Custom Validation Rules
- Create custom validation rules
- Use async validation when needed
- Validate business rules
- Use specifications for complex validation
- Keep validation rules focused

Example:
```csharp
public static class CustomValidationRules
{
    public static IRuleBuilderOptions<T, string> BeUniqueAccountNumber<T>(
        this IRuleBuilder<T, string> ruleBuilder,
        IRepository<BankAccount> repository)
    {
        return ruleBuilder.MustAsync(async (accountNumber, cancellationToken) =>
        {
            var spec = new GetBankAccountByNumberSpec(accountNumber);
            var existingAccount = await repository.FirstOrDefaultAsync(spec, cancellationToken);
            return existingAccount == null;
        });
    }
}
```

## Validation Error Handling
- Use proper error responses
- Include validation error details
- Use proper HTTP status codes
- Log validation errors
- Handle validation exceptions

Example:
```csharp
public class ValidationExceptionHandler : IExceptionHandler
{
    public async ValueTask<bool> TryHandleAsync(
        HttpContext context,
        Exception exception,
        CancellationToken cancellationToken)
    {
        if (exception is ValidationException validationException)
        {
            var response = new ErrorResponse
            {
                Message = "Validation failed",
                Errors = validationException.Errors
                    .GroupBy(x => x.PropertyName)
                    .ToDictionary(
                        g => g.Key,
                        g => g.Select(x => x.ErrorMessage).ToArray())
            };

            context.Response.StatusCode = StatusCodes.Status422UnprocessableEntity;
            await context.Response.WriteAsJsonAsync(response, cancellationToken);
            return true;
        }

        return false;
    }
}
```

## Validation Best Practices
- Validate early
- Validate often
- Use async validation when needed
- Keep validation rules focused
- Use proper error messages
- Use proper HTTP status codes
- Include validation error details
- Log validation errors
- Handle validation exceptions
- Use proper validation patterns

