---
description: Guidelines for testing the application with Vitest
globs: 
alwaysApply: false
---
# Testing Guidelines

## Unit Testing
- Test business logic in isolation
- Use xUnit as the testing framework
- Follow AAA pattern (Arrange, Act, Assert)
- Use Moq for mocking dependencies
- Test edge cases and error conditions

Example:
```csharp
public class BankAccountTests
{
    [Fact]
    public async Task CreateBankAccount_WithValidData_ShouldSucceed()
    {
        // Arrange
        var command = new CreateBankAccountCommand
        {
            AccountNumber = "**********",
            AccountHolderName = "John Doe",
            Balance = 1000,
            AccountType = AccountType.Savings
        };

        var validator = new BankAccountRequestDtoValidator(Mock.Of<IRepository<BankAccount>>());

        // Act
        var result = await validator.ValidateAsync(command);

        // Assert
        Assert.True(result.IsValid);
    }
}
```

## Integration Testing
- Test component interactions
- Use TestServer for API testing
- Use in-memory database for data access tests
- Test complete use cases
- Verify external service integration

Example:
```csharp
public class BankAccountEndpointTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;

    [Fact]
    public async Task CreateBankAccount_ReturnsSuccessResult()
    {
        // Arrange
        var client = _factory.CreateClient();
        var request = new CreateBankAccountRequest
        {
            AccountNumber = "**********",
            AccountHolderName = "John Doe",
            Balance = 1000,
            AccountType = AccountType.Savings
        };

        // Act
        var response = await client.PostAsJsonAsync("/api/bankaccounts", request);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadFromJsonAsync<BankAccountResponseDto>();
        Assert.NotNull(result);
        Assert.Equal(request.AccountNumber, result.AccountNumber);
    }
}
```

## Test Organization
- Mirror the project structure in tests
- Use descriptive test names
- Group related tests in classes
- Use test categories when needed
- Keep tests independent

## Test Data
- Use test data builders
- Avoid magic numbers
- Use constants for test data
- Consider using AutoFixture
- Keep test data realistic

Example:
```csharp
public class BankAccountBuilder
{
    private BankAccount _account = new();

    public BankAccountBuilder WithAccountNumber(string accountNumber)
    {
        _account.AccountNumber = accountNumber;
        return this;
    }

    public BankAccountBuilder WithBalance(decimal balance)
    {
        _account.Balance = balance;
        return this;
    }

    public BankAccount Build() => _account;
}
```

## Test Coverage
- Aim for high coverage of business logic
- Focus on critical paths
- Don't test implementation details
- Use coverage tools (e.g., Coverlet)
- Document uncovered scenarios

## Performance Testing
- Test response times
- Test under load
- Monitor resource usage
- Test concurrent operations
- Use appropriate tools (e.g., k6)

## Test Categories
- Unit tests for business logic
- Integration tests for component interaction
- End-to-end tests for complete flows
- Performance tests for critical paths
- Security tests for vulnerabilities

## Test Data Management
- Use test databases
- Clean up test data
- Use transactions when appropriate
- Avoid test interdependencies
- Use appropriate test data isolation

## Mocking Guidelines
- Mock external dependencies
- Use appropriate mock types
- Verify mock interactions
- Keep mocks simple
- Use mock factories when needed

Example:
```csharp
public class BankAccountServiceTests
{
    [Fact]
    public async Task GetBankAccount_ShouldReturnAccount()
    {
        // Arrange
        var mockRepo = new Mock<IRepository<BankAccount>>();
        var account = new BankAccount { AccountNumber = "**********" };
        mockRepo.Setup(x => x.GetByIdAsync(It.IsAny<Guid>()))
                .ReturnsAsync(account);

        var service = new BankAccountService(mockRepo.Object);

        // Act
        var result = await service.GetBankAccount(Guid.NewGuid());

        // Assert
        Assert.NotNull(result);
        Assert.Equal(account.AccountNumber, result.AccountNumber);
        mockRepo.Verify(x => x.GetByIdAsync(It.IsAny<Guid>()), Times.Once);
    }
}
```