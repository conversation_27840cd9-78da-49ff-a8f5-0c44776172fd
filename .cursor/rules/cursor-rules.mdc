---
description: How to add or edit Cursor rules in our project
globs: 
alwaysApply: false
---
# Cursor Rules Location

How to add new cursor rules to the project

1. Always place rule files in PROJECT_ROOT/.cursor/rules/:
    ```
    .cursor/rules/
    ├── your-rule-name.mdc
    ├── another-rule.mdc
    └── ...
    ```

2. Follow the naming convention:
    - Use kebab-case for filenames
    - Always use .mdc extension
    - Make names descriptive of the rule's purpose

3. Directory structure:
    ```
    PROJECT_ROOT/
    ├── .cursor/
    │   └── rules/
    │       ├── your-rule-name.mdc
    │       └── ...
    └── ...
    ```

4. Never place rule files:
    - In the project root
    - In subdirectories outside .cursor/rules
    - In any other location

5. Cursor rules have the following structure:

# Clean Architecture C# Project Rules

## Project Structure
- The solution follows Clean Architecture principles with distinct layers:
  - Core: Contains domain entities, interfaces, and business rules
  - UseCases: Contains application business rules and use cases
  - Infrastructure: Contains external interfaces implementations (databases, external services)
  - Web: Contains API controllers and web-specific code

## Naming Conventions
- Use PascalCase for all public members, classes, and interfaces
- Use camelCase for private fields and parameters
- Suffix interfaces with 'I' (e.g., IRepository)
- Use descriptive names that reflect the domain language

## Code Organization
- Keep domain entities in the Core layer
- Place business logic in UseCases layer
- Implement interfaces in Infrastructure layer
- Keep controllers thin, delegating to use cases
- Use dependency injection for all dependencies

## Testing
- Write unit tests for use cases
- Write integration tests for infrastructure implementations
- Use xUnit as the testing framework
- Follow AAA pattern (Arrange, Act, Assert)

## Error Handling
- Use custom exceptions for domain-specific errors
- Implement global exception handling middleware
- Return appropriate HTTP status codes
- Include meaningful error messages

## Documentation
- Document public APIs using XML comments
- Keep README.md up to date
- Document complex business rules
- Include setup instructions in documentation

## Security
- Use HTTPS in production
- Implement proper authentication and authorization
- Follow OWASP security guidelines
- Never expose sensitive information in logs or responses

## Performance
- Use async/await for I/O operations
- Implement proper caching strategies
- Optimize database queries
- Use appropriate data structures

## Dependency Management
- Use NuGet for package management
- Keep dependencies up to date
- Document third-party dependencies
- Use semantic versioning
