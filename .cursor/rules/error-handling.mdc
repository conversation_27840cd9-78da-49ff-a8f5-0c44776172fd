---
description: 
globs: 
alwaysApply: false
---
# Error Handling Guidelines

## Global Exception Handler
- Use global exception handler
- Handle different exception types
- Return proper error responses
- Use proper HTTP status codes
- Include error details

Example:
```csharp
public class GlobalExceptionHandler : IExceptionHandler
{
    private readonly ILogger<GlobalExceptionHandler> _logger;

    public GlobalExceptionHandler(ILogger<GlobalExceptionHandler> logger)
    {
        _logger = logger;
    }

    public async ValueTask<bool> TryHandleAsync(
        HttpContext context,
        Exception exception,
        CancellationToken cancellationToken)
    {
        _logger.LogError(exception, "An unhandled exception occurred");

        var response = new ErrorResponse
        {
            Message = exception.Message,
            StatusCode = GetStatusCode(exception)
        };

        context.Response.StatusCode = response.StatusCode;
        await context.Response.WriteAsJsonAsync(response, cancellationToken);
        return true;
    }

    private static int GetStatusCode(Exception exception) => exception switch
    {
        ValidationException => StatusCodes.Status422UnprocessableEntity,
        NotFoundException => StatusCodes.Status404NotFound,
        UnauthorizedAccessException => StatusCodes.Status401Unauthorized,
        _ => StatusCodes.Status500InternalServerError
    };
}
```

## Custom Exceptions
- Create custom exceptions
- Use proper exception types
- Include error details
- Use proper error messages
- Follow exception hierarchy

Example:
```csharp
public class NotFoundException : Exception
{
    public NotFoundException(string message) : base(message)
    {
    }

    public NotFoundException(string name, object key)
        : base($"Entity \"{name}\" ({key}) was not found.")
    {
    }
}
```

## Error Responses
- Use consistent error response format
- Include error details
- Use proper HTTP status codes
- Include error messages
- Include error codes

Example:
```csharp
public class ErrorResponse
{
    public string Message { get; set; } = string.Empty;
    public int StatusCode { get; set; }
    public IDictionary<string, string[]>? Errors { get; set; }
    public string? TraceId { get; set; }
}
```

## Error Logging
- Log errors appropriately
- Include error details
- Use proper log levels
- Include correlation IDs
- Use structured logging

Example:
```csharp
public class ErrorLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ErrorLoggingMiddleware> _logger;

    public ErrorLoggingMiddleware(RequestDelegate next, ILogger<ErrorLoggingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while processing the request");
            throw;
        }
    }
}
```

## Error Handling Best Practices
- Handle errors at appropriate levels
- Use proper exception types
- Include error details
- Use proper HTTP status codes
- Log errors appropriately
- Use proper error messages
- Include error codes
- Use proper error response format
- Handle validation errors
- Handle business rule violations
- Handle infrastructure errors
- Handle security errors
- Handle performance errors
- Handle concurrent access errors
- Handle data integrity errors
- Handle external service errors
- Handle network errors
- Handle timeout errors
- Handle resource errors
- Handle configuration errors

